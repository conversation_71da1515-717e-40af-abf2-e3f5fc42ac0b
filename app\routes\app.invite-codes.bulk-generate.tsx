import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { useActionData, useNavigation, useSubmit, useLoaderData } from "@remix-run/react";
import { useState } from "react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  FormLayout,
  TextField,
  Select,
  Button,
  InlineStack,
  Banner,
  Spinner,
  BlockStack,
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import { getPartnersForDropdown } from "../models/partner.server";
import { getMembershipTagsForDropdown } from "../models/membership-tag.server";
import { ThemeToggle } from "../components/UI/ThemeToggle";
import { formatDateForInput } from "../utils/dateFormatter";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Load membership plans and partners for the form
    const [membershipPlans, partners] = await Promise.all([
      getMembershipTagsForDropdown(shop),
      getPartnersForDropdown(shop),
    ]);

    return json({
      shop,
      membershipPlans,
      partners: partners.map(p => ({ value: p.id.toString(), label: p.name })),
    });
  } catch (error) {
    console.error("Error loading form data:", error);
    return json({
      shop,
      membershipPlans: [],
      partners: [],
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  console.log("🔄 Processing bulk generation request...");
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    const formData = await request.formData();
    const data = Object.fromEntries(formData) as any;
    
    console.log("📝 Bulk generation data:", data);

    // Validate and parse form data
    const bulkData = {
      numberOfCodes: parseInt(data.numberOfCodes?.toString() || "0"),
      maxUsesPerCode: parseInt(data.maxUsesPerCode?.toString() || "1"),
      expiryDate: data.expiryDate?.toString() || undefined,
      membershipId: data.membershipId?.toString() || "",
      partnerId: data.partnerId ? parseInt(data.partnerId.toString()) : undefined,
    };

    // Validation
    if (bulkData.numberOfCodes < 1 || bulkData.numberOfCodes > 1000) {
      return json(
        { error: "Number of codes must be between 1 and 1000", success: false },
        { status: 400 }
      );
    }

    if (bulkData.maxUsesPerCode < 1) {
      return json(
        { error: "Maximum uses per code must be at least 1", success: false },
        { status: 400 }
      );
    }

    if (!bulkData.membershipId) {
      return json(
        { error: "Membership plan is required", success: false },
        { status: 400 }
      );
    }

    // Validate expiry date if provided
    let expiresAt: Date | undefined;
    if (bulkData.expiryDate) {
      expiresAt = new Date(bulkData.expiryDate);
      if (expiresAt <= new Date()) {
        return json(
          { error: "Expiry date must be in the future", success: false },
          { status: 400 }
        );
      }
    }

    // Call the bulk generation API
    const response = await fetch(`${request.url.split('/app')[0]}/api/invite-codes/bulk-generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...bulkData,
        expiresAt: expiresAt?.toISOString(),
        createdBy: session.id,
        shopId: shop,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return json(
        { error: errorData.error || "Failed to generate codes", success: false },
        { status: response.status }
      );
    }

    const result = await response.json();
    console.log("✅ Successfully generated bulk codes:", result.codes.length);

    return json({
      success: true,
      codes: result.codes,
      message: `Successfully generated ${result.codes.length} invite codes`,
    });

  } catch (error: any) {
    console.error("❌ Error in bulk generation:", error);
    return json(
      { error: "Failed to generate codes. Please try again.", success: false },
      { status: 500 }
    );
  }
};

export default function BulkGeneratePage() {
  const actionData = useActionData<typeof action>();
  const loaderData = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const submit = useSubmit();

  const [numberOfCodes, setNumberOfCodes] = useState("");
  const [maxUsesType, setMaxUsesType] = useState("1");
  const [customMaxUses, setCustomMaxUses] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [membershipId, setMembershipId] = useState("");
  const [partnerId, setPartnerId] = useState("");

  const isSubmitting = navigation.state === "submitting";

  const maxUsesOptions = [
    { label: "1 use", value: "1" },
    { label: "5 uses", value: "5" },
    { label: "10 uses", value: "10" },
    { label: "25 uses", value: "25" },
    { label: "50 uses", value: "50" },
    { label: "100 uses", value: "100" },
    { label: "Custom", value: "custom" },
  ];

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData();

    // Set all form values
    formData.set("numberOfCodes", numberOfCodes);
    const finalMaxUses = maxUsesType === "custom" ? customMaxUses : maxUsesType;
    formData.set("maxUsesPerCode", finalMaxUses);
    formData.set("expiryDate", expiryDate);
    formData.set("membershipId", membershipId);
    formData.set("partnerId", partnerId);

    submit(formData, { method: "POST" });
  };

  const downloadCSV = () => {
    if (!actionData?.success || !actionData.codes) return;

    // Enhanced CSV with more comprehensive data
    const csvHeaders = [
      "Code",
      "Max Uses",
      "Used Count",
      "Remaining Uses",
      "Status",
      "Expires At",
      "Membership Plan",
      "Partner",
      "Created At",
      "Shop"
    ];

    const csvRows = actionData.codes.map((code: any) => [
      `"${code.code}"`, // Wrap in quotes to handle special characters
      code.maxUses,
      code.usedCount || 0,
      (code.maxUses - (code.usedCount || 0)),
      code.isActive ? "Active" : "Inactive",
      code.expiresAt ? `"${new Date(code.expiresAt).toLocaleDateString()}"` : "No expiry",
      code.membershipId ? `"Membership ${code.membershipId}"` : "No membership",
      code.partnerId ? `"Partner ${code.partnerId}"` : "No partner",
      `"${new Date(code.createdAt).toLocaleDateString()}"`,
      `"${loaderData.shop}"`
    ].join(","));

    const csvContent = [csvHeaders.join(","), ...csvRows].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `supertested-invite-codes-${timestamp}.csv`;
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log(`📥 Downloaded CSV with ${actionData.codes.length} codes: ${filename}`);
  };

  return (
    <Page
      title="Bulk Generate Invite Codes"
      subtitle="Generate multiple invite codes at once"
      backAction={{
        content: "Back to Invite Codes",
        url: "/app/invite-codes",
      }}
    >
      <Box paddingBlockEnd="400">
        <InlineStack align="end">
          <ThemeToggle />
        </InlineStack>
      </Box>

      <Layout>
        <Layout.Section variant="oneHalf">
          <Card>
            <Box padding="400">
              <Text variant="headingMd" as="h2">
                Bulk Generation Settings
              </Text>

              {actionData?.error && (
                <Box paddingBlockStart="400">
                  <Banner tone="critical">
                    <p>{actionData.error}</p>
                  </Banner>
                </Box>
              )}

              {actionData?.success && (
                <Box paddingBlockStart="400">
                  <Banner tone="success" title="Bulk Generation Successful!">
                    <p>{actionData.message}</p>
                    <p>You can now download the CSV file or view the generated codes in the right panel.</p>
                  </Banner>
                </Box>
              )}

              <Box paddingBlockStart="400">
                <form onSubmit={handleSubmit}>
                  <FormLayout>
                    <TextField
                      label="Number of Codes"
                      type="number"
                      min={1}
                      max={1000}
                      value={numberOfCodes}
                      onChange={setNumberOfCodes}
                      placeholder="Enter number of codes to generate"
                      helpText="Maximum 1000 codes per batch"
                      autoComplete="off"
                      disabled={isSubmitting}
                      required
                    />

                    <Select
                      label="Maximum Uses per Code"
                      options={maxUsesOptions}
                      value={maxUsesType}
                      onChange={setMaxUsesType}
                      helpText="How many times each code can be used"
                      disabled={isSubmitting}
                    />

                    {maxUsesType === "custom" && (
                      <TextField
                        label="Custom Max Uses"
                        type="number"
                        min={1}
                        value={customMaxUses}
                        onChange={setCustomMaxUses}
                        placeholder="Enter custom number"
                        autoComplete="off"
                        disabled={isSubmitting}
                        required
                      />
                    )}

                    <TextField
                      label="Expiry Date & Time (Optional)"
                      type="datetime-local"
                      value={expiryDate}
                      onChange={setExpiryDate}
                      helpText="Format: YYYY/MM/DD HH:MM - Leave empty for no expiry"
                      autoComplete="off"
                      disabled={isSubmitting}
                    />

                    <Select
                      label="Membership Plan *"
                      value={membershipId}
                      onChange={setMembershipId}
                      options={[
                        { label: "Select a membership plan...", value: "" },
                        ...loaderData.membershipPlans,
                      ]}
                      helpText="All codes will be assigned to this membership plan"
                      disabled={isSubmitting}
                      required
                    />

                    <Select
                      label="Partner (Optional)"
                      value={partnerId}
                      onChange={setPartnerId}
                      options={[
                        { label: "No partner assigned", value: "" },
                        ...loaderData.partners,
                      ]}
                      helpText="Optionally assign all codes to a partner"
                      disabled={isSubmitting}
                    />

                    <InlineStack align="end" gap="300">
                      <Button
                        variant="secondary"
                        url="/app/invite-codes"
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="primary"
                        submit
                        loading={isSubmitting}
                        disabled={isSubmitting || actionData?.success}
                      >
                        {isSubmitting ? "Generating..." : actionData?.success ? "✅ Generated!" : "Generate Codes"}
                      </Button>
                    </InlineStack>
                  </FormLayout>
                </form>
              </Box>
            </Box>
          </Card>
        </Layout.Section>

        <Layout.Section variant="oneHalf">
          {actionData?.success && actionData.codes ? (
            <Card>
              <Box padding="400">
                <Text variant="headingMd" as="h3">
                  ✅ Generation Complete!
                </Text>
                <Box paddingBlockStart="400">
                  <Text variant="bodyMd" as="p" tone="success">
                    Successfully generated <strong>{actionData.codes.length} invite codes</strong>.
                  </Text>

                  <Box paddingBlockStart="400">
                    <Text variant="headingSm" as="h4">
                      Sample Generated Codes:
                    </Text>
                    <Box paddingBlockStart="200">
                      <div style={{
                        backgroundColor: "#f6f6f7",
                        padding: "12px",
                        borderRadius: "6px",
                        fontFamily: "monospace",
                        fontSize: "14px"
                      }}>
                        {actionData.codes.slice(0, 5).map((code: any, index: number) => (
                          <div key={index} style={{ marginBottom: "4px" }}>
                            <strong>{code.code}</strong> - {code.maxUses} uses
                            {code.expiresAt && ` - Expires: ${new Date(code.expiresAt).toLocaleDateString()}`}
                          </div>
                        ))}
                        {actionData.codes.length > 5 && (
                          <div style={{ color: "#666", fontStyle: "italic" }}>
                            ... and {actionData.codes.length - 5} more codes
                          </div>
                        )}
                      </div>
                    </Box>
                  </Box>

                  <Box paddingBlockStart="400">
                    <InlineStack gap="300">
                      <Button
                        variant="primary"
                        onClick={downloadCSV}
                      >
                        📥 Download CSV Export
                      </Button>
                      <Button
                        url="/app/invite-codes"
                        variant="secondary"
                      >
                        View All Codes
                      </Button>
                    </InlineStack>
                  </Box>
                </Box>
              </Box>
            </Card>
          ) : (
            <Card>
              <Box padding="400">
                <Text variant="headingMd" as="h3">
                  Bulk Generation Tips
                </Text>
                <Box paddingBlockStart="400">
                  <Text variant="bodyMd" as="p">
                    Here are some tips for bulk generation:
                  </Text>
                  <Box paddingBlockStart="300">
                    <ul>
                      <li>Generate up to 1000 codes at once</li>
                      <li>All codes will have the same settings</li>
                      <li>Codes are automatically generated with unique values</li>
                      <li>Download the CSV file to share or import codes</li>
                      <li>Consider setting expiry dates for time-limited campaigns</li>
                    </ul>
                  </Box>
                </Box>
              </Box>
            </Card>
          )}
        </Layout.Section>
      </Layout>
    </Page>
  );
}
