import { useState, useCallback } from "react";
import { <PERSON> } from "@remix-run/react";
import {
  Card,
  ResourceList,
  ResourceItem,
  Text,
  InlineStack,
  Button,
  Pagination,
  Filters,
  ChoiceList,
  TextField,
  EmptyState,
  Modal,
  Box,
  Tooltip,
  Select,
} from "@shopify/polaris";
import { EditIcon, DeleteIcon, ViewIcon, CheckIcon, XIcon } from "@shopify/polaris-icons";
import { CodeBadge, UsageBadge, ExpiryBadge } from "./UI/CodeBadge";
import type { InviteCodeWithUsages } from "../models/invite-code.server";
import { formatDate } from "../utils/dateFormatter";

interface InviteCodeListProps {
  codes: InviteCodeWithUsages[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  onPageChange: (page: number) => void;
  onDelete: (id: number) => void;
  onBulkDelete: (codeIds: number[]) => void;
  onFiltersChange: (filters: InviteCodeFilters) => void;
  onToggleActive: (code: InviteCodeWithUsages) => void;
  loading?: boolean;
  partners?: Array<{ value: string; label: string }>;
  membershipPlans?: Array<{ value: string; label: string }>;
}

export interface InviteCodeFilters {
  search?: string;
  status?: string[];
  sortBy?: string;
  partnerId?: string;
  membershipId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export function InviteCodeList({
  codes,
  pagination,
  onPageChange,
  onDelete,
  onBulkDelete,
  onFiltersChange,
  onToggleActive,
  loading = false,
  partners = [],
  membershipPlans = [],
}: InviteCodeListProps) {
  const [filters, setFilters] = useState<InviteCodeFilters>({
    search: "",
    status: [],
    sortBy: "newest",
    partnerId: "",
    membershipId: "",
    dateFrom: "",
    dateTo: "",
  });

  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    codeId?: number;
    codeName?: string;
  }>({ isOpen: false });

  const handleFiltersChange = useCallback((newFilters: Partial<InviteCodeFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange(updatedFilters);
  }, [filters, onFiltersChange]);

  const handleSearchChange = useCallback((value: string) => {
    handleFiltersChange({ search: value });
  }, [handleFiltersChange]);

  const handleStatusFilterChange = useCallback((value: string[]) => {
    handleFiltersChange({ status: value });
  }, [handleFiltersChange]);

  const handleSortChange = useCallback((value: string) => {
    handleFiltersChange({ sortBy: value });
  }, [handleFiltersChange]);

  const handlePartnerFilterChange = useCallback((value: string) => {
    handleFiltersChange({ partnerId: value });
  }, [handleFiltersChange]);

  const handleMembershipFilterChange = useCallback((value: string) => {
    handleFiltersChange({ membershipId: value });
  }, [handleFiltersChange]);

  const handleDateFromChange = useCallback((value: string) => {
    handleFiltersChange({ dateFrom: value });
  }, [handleFiltersChange]);

  const handleDateToChange = useCallback((value: string) => {
    handleFiltersChange({ dateTo: value });
  }, [handleFiltersChange]);

  const handleDeleteClick = useCallback((code: InviteCodeWithUsages) => {
    setDeleteModal({
      isOpen: true,
      codeId: code.id,
      codeName: code.code,
    });
  }, []);

  const handleDeleteConfirm = useCallback(() => {
    if (deleteModal.codeId) {
      onDelete(deleteModal.codeId);
      setDeleteModal({ isOpen: false });
    }
  }, [deleteModal.codeId, onDelete]);

  const handleSelectionChange = useCallback((selectedResources: string[]) => {
    setSelectedItems(selectedResources);
  }, []);

  const handleBulkActivate = useCallback(() => {
    selectedItems.forEach(itemId => {
      const code = codes.find(c => c.id.toString() === itemId);
      if (code && !code.isActive) {
        onToggleActive(code);
      }
    });
    setSelectedItems([]);
  }, [selectedItems, codes, onToggleActive]);

  const handleBulkDeactivate = useCallback(() => {
    selectedItems.forEach(itemId => {
      const code = codes.find(c => c.id.toString() === itemId);
      if (code && code.isActive) {
        onToggleActive(code);
      }
    });
    setSelectedItems([]);
  }, [selectedItems, codes, onToggleActive]);

  const handleBulkDelete = useCallback(() => {
    const codeIds = selectedItems.map(itemId => parseInt(itemId));
    onBulkDelete(codeIds);
    setSelectedItems([]);
  }, [selectedItems, onBulkDelete]);

  const getCodeStatus = (code: InviteCodeWithUsages) => {
    if (!code.isActive) return "inactive";
    if (code.expiresAt && new Date(code.expiresAt) < new Date()) return "expired";
    const actualUsageCount = code.usages?.length || 0;
    if (actualUsageCount >= code.maxUses) return "used";
    return "active";
  };

  const statusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
    { label: "Expired", value: "expired" },
    { label: "Fully Used", value: "used" },
  ];

  const sortOptions = [
    { label: "Newest First", value: "newest" },
    { label: "Oldest First", value: "oldest" },
    { label: "Most Used", value: "mostUsed" },
    { label: "Least Used", value: "leastUsed" },
    { label: "Expiring Soon", value: "expiringSoon" },
  ];

  const appliedFilters = [];
  if (filters.search) {
    appliedFilters.push({
      key: "search",
      label: `Search: ${filters.search}`,
      onRemove: () => handleSearchChange(""),
    });
  }
  if (filters.status && filters.status.length > 0) {
    appliedFilters.push({
      key: "status",
      label: `Status: ${filters.status.join(", ")}`,
      onRemove: () => handleStatusFilterChange([]),
    });
  }
  if (filters.partnerId) {
    const partner = partners.find(p => p.value === filters.partnerId);
    appliedFilters.push({
      key: "partner",
      label: `Partner: ${partner?.label || filters.partnerId}`,
      onRemove: () => handlePartnerFilterChange(""),
    });
  }
  if (filters.membershipId) {
    const membership = membershipPlans.find(m => m.value === filters.membershipId);
    appliedFilters.push({
      key: "membership",
      label: `Membership: ${membership?.label || filters.membershipId}`,
      onRemove: () => handleMembershipFilterChange(""),
    });
  }
  if (filters.dateFrom) {
    appliedFilters.push({
      key: "dateFrom",
      label: `From: ${filters.dateFrom}`,
      onRemove: () => handleDateFromChange(""),
    });
  }
  if (filters.dateTo) {
    appliedFilters.push({
      key: "dateTo",
      label: `To: ${filters.dateTo}`,
      onRemove: () => handleDateToChange(""),
    });
  }

  const filterControl = (
    <Filters
      queryValue={filters.search || ""}
      queryPlaceholder="Search invite codes..."
      onQueryChange={handleSearchChange}
      onQueryClear={() => handleSearchChange("")}
      filters={[
        {
          key: "status",
          label: "Status",
          filter: (
            <ChoiceList
              title="Status"
              titleHidden
              choices={statusOptions}
              selected={filters.status || []}
              onChange={handleStatusFilterChange}
              allowMultiple
            />
          ),
          shortcut: true,
        },
        {
          key: "partner",
          label: "Partner",
          filter: (
            <Select
              label="Partner"
              labelHidden
              options={[
                { label: "All partners", value: "" },
                ...partners,
              ]}
              value={filters.partnerId || ""}
              onChange={handlePartnerFilterChange}
            />
          ),
        },
        {
          key: "membership",
          label: "Membership",
          filter: (
            <Select
              label="Membership"
              labelHidden
              options={[
                { label: "All memberships", value: "" },
                ...membershipPlans,
              ]}
              value={filters.membershipId || ""}
              onChange={handleMembershipFilterChange}
            />
          ),
        },
        {
          key: "dateRange",
          label: "Date Range",
          filter: (
            <div style={{ display: "flex", gap: "8px", flexDirection: "column" }}>
              <TextField
                label="From"
                type="date"
                value={filters.dateFrom || ""}
                onChange={handleDateFromChange}
                autoComplete="off"
              />
              <TextField
                label="To"
                type="date"
                value={filters.dateTo || ""}
                onChange={handleDateToChange}
                autoComplete="off"
              />
            </div>
          ),
        },
      ]}
      appliedFilters={appliedFilters}
      onClearAll={() => {
        handleSearchChange("");
        handleStatusFilterChange([]);
        handlePartnerFilterChange("");
        handleMembershipFilterChange("");
        handleDateFromChange("");
        handleDateToChange("");
      }}
    >
      <div style={{ paddingLeft: "8px" }}>
        <Select
          label="Sort by"
          labelHidden
          value={filters.sortBy || "newest"}
          onChange={handleSortChange}
          options={sortOptions}
        />
      </div>
    </Filters>
  );

  if (codes.length === 0 && !loading) {
    return (
      <Card>
        <EmptyState
          heading="No invite codes found"
          action={{
            content: "Create invite code",
            onAction: () => {
              // This would typically navigate to create form
            },
          }}
          image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
        >
          <p>Get started by creating your first invite code.</p>
        </EmptyState>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <ResourceList
          resourceName={{ singular: "invite code", plural: "invite codes" }}
          items={codes}
          loading={loading}
          filterControl={filterControl}
          selectedItems={selectedItems}
          onSelectionChange={handleSelectionChange}
          selectable
          promotedBulkActions={[
            {
              content: "Activate",
              onAction: handleBulkActivate,
            },
            {
              content: "Deactivate",
              onAction: handleBulkDeactivate,
            },
          ]}
          bulkActions={[
            {
              content: "Delete",
              onAction: handleBulkDelete,
            },
          ]}
          renderItem={(code) => {
            const status = getCodeStatus(code);

            return (
              <ResourceItem
                id={code.id.toString()}
                url="#"
                media={
                  <div className="code-list-media">
                    <CodeBadge
                      code={code.code}
                      status={status}
                      size="medium"
                    />
                  </div>
                }
                accessibilityLabel={`Invite code ${code.code}`}
              >
                <div className="code-list-item">
                  <div className="code-list-content">
                    <InlineStack align="space-between" blockAlign="start">
                      <div className="code-list-details">
                        <Text variant="bodyMd" fontWeight="medium" as="h3">
                          {code.code}
                        </Text>
                        
                        <Box paddingBlockStart="100">
                          <InlineStack gap="300" align="start">
                            <UsageBadge usedCount={code.usedCount || 0} maxUses={code.maxUses} />
                            <ExpiryBadge expiresAt={code.expiresAt} />
                          </InlineStack>
                        </Box>

                        <Box paddingBlockStart="200">
                          <InlineStack gap="400" align="start">
                            <Text variant="bodySm" tone="subdued" as="span">
                              Purchases: {code.usedCount || 0}/{code.maxUses}
                            </Text>
                            <Text variant="bodySm" tone="subdued" as="span">
                              Applications: {code.usedCount || 0}
                            </Text>
                            {code.partner && (
                              <Text variant="bodySm" tone="subdued" as="span">
                                Partner: {code.partner.name}
                              </Text>
                            )}
                          </InlineStack>
                        </Box>

                        <Box paddingBlockStart="200">
                          <Text variant="bodySm" tone="subdued" as="span">
                            Created {formatDate(code.createdAt)}
                            {code.usages.length > 0 && (
                              <> • Last used {formatDate(code.usages[0].usedAt)}</>
                            )}
                          </Text>
                        </Box>
                      </div>
                      
                      <div className="code-list-actions">
                        <InlineStack gap="200">
                          <Tooltip content={code.isActive ? "Deactivate code" : "Activate code"}>
                            <Button
                              variant="tertiary"
                              size="micro"
                              icon={code.isActive ? XIcon : CheckIcon}
                              tone={code.isActive ? "critical" : "success"}
                              onClick={() => onToggleActive(code)}
                              accessibilityLabel={`${code.isActive ? "Deactivate" : "Activate"} ${code.code}`}
                            />
                          </Tooltip>
                          <Link to={`/app/invite-codes/${code.id}`}>
                            <Button
                              variant="tertiary"
                              size="micro"
                              icon={ViewIcon}
                              accessibilityLabel={`View details for ${code.code}`}
                            />
                          </Link>
                          <Link to={`/app/invite-codes/edit/${code.id}`}>
                            <Button
                              variant="tertiary"
                              size="micro"
                              icon={EditIcon}
                              accessibilityLabel={`Edit ${code.code}`}
                            />
                          </Link>
                          <Button
                            variant="tertiary"
                            size="micro"
                            icon={DeleteIcon}
                            tone="critical"
                            onClick={() => handleDeleteClick(code)}
                            accessibilityLabel={`Delete ${code.code}`}
                          />
                        </InlineStack>
                      </div>
                    </InlineStack>
                  </div>
                </div>
              </ResourceItem>
            );
          }}
        />
        
        {pagination.pages > 1 && (
          <Box padding="400">
            <InlineStack align="center">
              <Pagination
                hasPrevious={pagination.page > 1}
                onPrevious={() => onPageChange(pagination.page - 1)}
                hasNext={pagination.page < pagination.pages}
                onNext={() => onPageChange(pagination.page + 1)}
                label={`Page ${pagination.page} of ${pagination.pages}`}
              />
            </InlineStack>
          </Box>
        )}
      </Card>

      <Modal
        open={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false })}
        title="Delete Invite Code"
        primaryAction={{
          content: "Delete",
          destructive: true,
          onAction: handleDeleteConfirm,
        }}
        secondaryActions={[
          {
            content: "Cancel",
            onAction: () => setDeleteModal({ isOpen: false }),
          },
        ]}
      >
        <Modal.Section>
          <Text as="p">
            Are you sure you want to delete the invite code "{deleteModal.codeName}"? 
            This action cannot be undone.
          </Text>
        </Modal.Section>
      </Modal>
    </>
  );
}
