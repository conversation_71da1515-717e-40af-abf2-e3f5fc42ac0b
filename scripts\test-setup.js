#!/usr/bin/env node

/**
 * Supertested Invite Code - Setup Test Script
 * This script tests the basic setup and configuration
 */

import fs from 'fs';
import path from 'path';

console.log('🧪 Supertested Invite Code - Setup Test');
console.log('=====================================\n');

let hasErrors = false;

// Test 1: Check if required files exist
console.log('📁 Checking required files...');
const requiredFiles = [
  '.env.example',
  'prisma/schema.prisma',
  'app/models/invite-code.server.ts',
  'app/components/Dashboard/StatsCard.tsx',
  'app/routes/app._index.tsx',
  'app/routes/api.validate-invite.tsx',
  'DATABASE_SETUP.md'
];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - Missing!`);
    hasErrors = true;
  }
});

// Test 2: Check environment variables
console.log('\n🔧 Checking environment configuration...');
if (fs.existsSync('.env')) {
  console.log('  ✅ .env file exists');
  
  const envContent = fs.readFileSync('.env', 'utf8');
  const requiredEnvVars = [
    'DATABASE_URL',
    'SHOPIFY_API_KEY',
    'SHOPIFY_API_SECRET',
    'SHOPIFY_SCOPES',
    'SHOPIFY_APP_URL'
  ];
  
  requiredEnvVars.forEach(envVar => {
    if (envContent.includes(`${envVar}=`)) {
      console.log(`  ✅ ${envVar} is defined`);
    } else {
      console.log(`  ⚠️  ${envVar} is not defined`);
    }
  });
} else {
  console.log('  ⚠️  .env file not found (copy from .env.example)');
}

// Test 3: Check package.json dependencies
console.log('\n📦 Checking dependencies...');
if (fs.existsSync('package.json')) {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = [
    '@shopify/polaris',
    '@prisma/client',
    'prisma',
    'pg',
    '@types/pg'
  ];
  
  const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  requiredDeps.forEach(dep => {
    if (allDeps[dep]) {
      console.log(`  ✅ ${dep} v${allDeps[dep]}`);
    } else {
      console.log(`  ❌ ${dep} - Missing!`);
      hasErrors = true;
    }
  });
} else {
  console.log('  ❌ package.json not found!');
  hasErrors = true;
}

// Test 4: Check Prisma schema
console.log('\n🗄️  Checking database schema...');
if (fs.existsSync('prisma/schema.prisma')) {
  const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
  
  if (schemaContent.includes('provider = "postgresql"')) {
    console.log('  ✅ PostgreSQL provider configured');
  } else {
    console.log('  ⚠️  PostgreSQL provider not configured');
  }
  
  if (schemaContent.includes('model InviteCode')) {
    console.log('  ✅ InviteCode model exists');
  } else {
    console.log('  ❌ InviteCode model missing!');
    hasErrors = true;
  }
  
  if (schemaContent.includes('model Usage')) {
    console.log('  ✅ Usage model exists');
  } else {
    console.log('  ❌ Usage model missing!');
    hasErrors = true;
  }
} else {
  console.log('  ❌ Prisma schema not found!');
  hasErrors = true;
}

// Test 5: Check TypeScript configuration
console.log('\n⚙️  Checking TypeScript configuration...');
if (fs.existsSync('tsconfig.json')) {
  console.log('  ✅ TypeScript configuration exists');
} else {
  console.log('  ❌ TypeScript configuration missing!');
  hasErrors = true;
}

// Test 6: Check custom components
console.log('\n🎨 Checking custom components...');
const componentFiles = [
  'app/components/UI/ThemeToggle.tsx',
  'app/components/UI/Button.tsx',
  'app/components/UI/Card.tsx',
  'app/components/UI/CodeBadge.tsx',
  'app/components/InviteCodeForm.tsx',
  'app/components/InviteCodeList.tsx'
];

componentFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${path.basename(file)}`);
  } else {
    console.log(`  ❌ ${path.basename(file)} - Missing!`);
    hasErrors = true;
  }
});

// Test 7: Check API routes
console.log('\n🌐 Checking API routes...');
const apiRoutes = [
  'app/routes/api.validate-invite.tsx',
  'app/routes/api.docs.tsx',
  'app/routes/validate.tsx'
];

apiRoutes.forEach(route => {
  if (fs.existsSync(route)) {
    console.log(`  ✅ ${path.basename(route)}`);
  } else {
    console.log(`  ❌ ${path.basename(route)} - Missing!`);
    hasErrors = true;
  }
});

// Test 8: Check styles
console.log('\n🎨 Checking custom styles...');
const styleFiles = [
  'app/styles/theme.css',
  'app/styles/accessibility.css',
  'app/styles/animations.css'
];

styleFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${path.basename(file)}`);
  } else {
    console.log(`  ❌ ${path.basename(file)} - Missing!`);
    hasErrors = true;
  }
});

// Summary
console.log('\n📋 Test Summary');
console.log('===============');

if (hasErrors) {
  console.log('❌ Some tests failed! Please check the missing files and fix the issues.');
  console.log('\n🔧 Next steps:');
  console.log('1. Ensure all required files are present');
  console.log('2. Copy .env.example to .env and configure your environment');
  console.log('3. Install dependencies: npm install');
  console.log('4. Set up your PostgreSQL database');
  console.log('5. Run database migrations: npx prisma migrate dev');
  process.exit(1);
} else {
  console.log('✅ All tests passed! Your setup looks good.');
  console.log('\n🚀 Next steps:');
  console.log('1. Configure your .env file with actual values');
  console.log('2. Set up your PostgreSQL database');
  console.log('3. Run: npx prisma migrate dev --name init');
  console.log('4. Start development: npm run dev');
  console.log('5. Test your app with Shopify CLI');
  
  console.log('\n📚 Documentation:');
  console.log('- Database setup: ./DATABASE_SETUP.md');
  console.log('- API docs: http://localhost:3000/api/docs (after starting)');
  console.log('- Validation page: http://localhost:3000/validate (after starting)');
}

console.log('\n🎉 Happy coding!');
