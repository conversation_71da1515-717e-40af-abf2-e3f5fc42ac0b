import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { getInviteCodeByCode, validateAndUseInviteCode } from "../models/invite-code.server";
import { cors } from "../utils/cors.server";

/**
 * Public API endpoint for testing invite codes
 * This endpoint allows testing actual invite codes created in the app
 * without requiring authentication, making it useful for external testing
 */

export const loader = async ({ request }: LoaderFunctionArgs) => {
  // Handle CORS preflight requests
  if (request.method === "OPTIONS") {
    return cors(request, new Response(null, { status: 200 }));
  }

  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const shop = url.searchParams.get("shop");

  if (!code) {
    return cors(request, json({
      success: false,
      error: "Missing required parameter: code",
      usage: "GET /api/test-invite-code?code=YOUR_CODE&shop=your-shop.myshopify.com"
    }, { status: 400 }));
  }

  try {
    console.log(`🧪 Testing invite code: ${code} for shop: ${shop}`);
    
    // Get the invite code details without using it
    const inviteCode = await getInviteCodeByCode(code);
    
    if (!inviteCode) {
      return cors(request, json({
        success: false,
        error: "Invite code not found",
        code,
        shop,
        timestamp: new Date().toISOString(),
      }, { status: 404 }));
    }

    // Check if shop matches (if provided)
    if (shop && inviteCode.shopId !== shop) {
      return cors(request, json({
        success: false,
        error: "Invite code does not belong to the specified shop",
        code,
        shop,
        actualShop: inviteCode.shopId,
        timestamp: new Date().toISOString(),
      }, { status: 400 }));
    }

    // Validate the code without actually using it
    const isValid = inviteCode.isActive && 
                   (!inviteCode.expiresAt || inviteCode.expiresAt > new Date());
    
    const usageCount = inviteCode.usages?.length || 0;
    const canBeUsed = usageCount < inviteCode.maxUses;

    return cors(request, json({
      success: true,
      valid: isValid && canBeUsed,
      code: inviteCode.code,
      details: {
        id: inviteCode.id,
        shopId: inviteCode.shopId,
        isActive: inviteCode.isActive,
        maxUses: inviteCode.maxUses,
        currentUsage: usageCount,
        remainingUses: Math.max(0, inviteCode.maxUses - usageCount),
        expiresAt: inviteCode.expiresAt,
        expired: inviteCode.expiresAt ? inviteCode.expiresAt < new Date() : false,
        membershipId: inviteCode.membershipId,
        partnerId: inviteCode.partnerId,
        applicationsCount: inviteCode.applicationsCount,
        purchasesCount: inviteCode.purchasesCount,
        createdAt: inviteCode.createdAt,
        createdBy: inviteCode.createdBy,
      },
      reasons: {
        inactive: !inviteCode.isActive,
        expired: inviteCode.expiresAt ? inviteCode.expiresAt < new Date() : false,
        maxUsesReached: usageCount >= inviteCode.maxUses,
      },
      timestamp: new Date().toISOString(),
    }));

  } catch (error) {
    console.error("❌ Error testing invite code:", error);
    return cors(request, json({
      success: false,
      error: "Internal server error while testing invite code",
      code,
      shop,
      timestamp: new Date().toISOString(),
    }, { status: 500 }));
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  // Handle CORS preflight requests
  if (request.method === "OPTIONS") {
    return cors(request, new Response(null, { status: 200 }));
  }

  if (request.method !== "POST") {
    return cors(request, json({
      success: false,
      error: "Method not allowed. Use POST to test and simulate usage.",
    }, { status: 405 }));
  }

  try {
    const body = await request.json();
    const { code, shop, simulate = false, metadata = {} } = body;

    if (!code) {
      return cors(request, json({
        success: false,
        error: "Missing required field: code",
        usage: "POST /api/test-invite-code with { code: 'YOUR_CODE', shop: 'your-shop.myshopify.com', simulate: true }"
      }, { status: 400 }));
    }

    console.log(`🧪 Testing invite code: ${code} (simulate: ${simulate})`);

    if (simulate) {
      // Simulate usage without actually recording it
      const inviteCode = await getInviteCodeByCode(code);
      
      if (!inviteCode) {
        return cors(request, json({
          success: false,
          error: "Invite code not found",
          code,
          timestamp: new Date().toISOString(),
        }, { status: 404 }));
      }

      // Check if shop matches (if provided)
      if (shop && inviteCode.shopId !== shop) {
        return cors(request, json({
          success: false,
          error: "Invite code does not belong to the specified shop",
          code,
          shop,
          actualShop: inviteCode.shopId,
          timestamp: new Date().toISOString(),
        }, { status: 400 }));
      }

      // Simulate validation
      const usageCount = inviteCode.usages?.length || 0;
      const wouldBeValid = inviteCode.isActive && 
                          (!inviteCode.expiresAt || inviteCode.expiresAt > new Date()) &&
                          usageCount < inviteCode.maxUses;

      return cors(request, json({
        success: true,
        simulation: true,
        wouldBeValid,
        code: inviteCode.code,
        details: {
          shopId: inviteCode.shopId,
          membershipId: inviteCode.membershipId,
          partnerId: inviteCode.partnerId,
          currentUsage: usageCount,
          maxUses: inviteCode.maxUses,
          remainingUses: Math.max(0, inviteCode.maxUses - usageCount),
          expiresAt: inviteCode.expiresAt,
        },
        simulatedResult: wouldBeValid ? "Code would be accepted and usage recorded" : "Code would be rejected",
        reasons: {
          inactive: !inviteCode.isActive,
          expired: inviteCode.expiresAt ? inviteCode.expiresAt < new Date() : false,
          maxUsesReached: usageCount >= inviteCode.maxUses,
        },
        timestamp: new Date().toISOString(),
      }));

    } else {
      // Actually validate and use the code (for real testing)
      const result = await validateAndUseInviteCode(code, {
        customerId: metadata.customerId,
        ipAddress: metadata.ipAddress || request.headers.get("x-forwarded-for") || "unknown",
        userAgent: metadata.userAgent || request.headers.get("user-agent") || "test-api",
      });

      if (result.valid && result.inviteCode) {
        return cors(request, json({
          success: true,
          valid: true,
          used: true,
          code: result.inviteCode.code,
          details: {
            shopId: result.inviteCode.shopId,
            membershipId: result.inviteCode.membershipId,
            partnerId: result.inviteCode.partnerId,
            applicationsCount: result.inviteCode.applicationsCount,
            purchasesCount: result.inviteCode.purchasesCount,
          },
          message: "Invite code successfully validated and usage recorded",
          timestamp: new Date().toISOString(),
        }));
      } else {
        return cors(request, json({
          success: false,
          valid: false,
          code,
          reason: result.reason,
          message: `Invite code validation failed: ${result.reason}`,
          timestamp: new Date().toISOString(),
        }, { status: 400 }));
      }
    }

  } catch (error) {
    console.error("❌ Error in invite code test endpoint:", error);
    return cors(request, json({
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    }, { status: 500 }));
  }
};
