import type { LoaderFunctionArgs } from "@remix-run/node";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const baseUrl = new URL(request.url).origin;
  
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invite Code Testing Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #3498db;
        }
        
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .quick-codes {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        
        .quick-code {
            background: #e8f4fd;
            border: 1px solid #3498db;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .quick-code:hover {
            background: #3498db;
            color: white;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Invite Code Tester</h1>
            <p>Test your Supertested invite codes and simulate orders</p>
        </div>
        
        <div class="content">
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="testsRun">0</div>
                    <div class="stat-label">Tests Run</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successfulTests">0</div>
                    <div class="stat-label">Successful</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failedTests">0</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="simulatedOrders">0</div>
                    <div class="stat-label">Simulated Orders</div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🔍 Test Invite Code</h3>
                <p><strong>Order Tagging:</strong> Orders are automatically tagged as <code>INVITECODE-PARTNERNAME</code> for sales tracking.</p>
                <div class="form-group">
                    <label for="testCode">Invite Code:</label>
                    <input type="text" id="testCode" placeholder="Enter invite code (e.g., WELCOME10)" />
                    <div class="quick-codes">
                        <div class="quick-code" onclick="setCode('WELCOME10')">WELCOME10</div>
                        <div class="quick-code" onclick="setCode('SAVE20')">SAVE20</div>
                        <div class="quick-code" onclick="setCode('NEWUSER')">NEWUSER</div>
                        <div class="quick-code" onclick="setCode('PREMIUM')">PREMIUM</div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="shopDomain">Shop Domain (optional):</label>
                    <input type="text" id="shopDomain" placeholder="supertested-staging.myshopify.com" value="supertested-staging.myshopify.com" />
                </div>
                <button class="btn" onclick="testCode()">🔍 Test Code</button>
                <button class="btn btn-success" onclick="simulateUsage()">✅ Simulate Usage</button>
                <button class="btn btn-warning" onclick="actualUsage()">⚠️ Actual Usage</button>
                <button class="btn btn-danger" onclick="simulateOrder()">🛒 Simulate Order</button>
            </div>
            
            <div class="test-section">
                <h3>📊 Bulk Testing</h3>
                <div class="form-group">
                    <label for="bulkCodes">Codes (one per line):</label>
                    <textarea id="bulkCodes" rows="4" placeholder="WELCOME10\nSAVE20\nNEWUSER\nPREMIUM"></textarea>
                </div>
                <button class="btn" onclick="bulkTest()">🔄 Bulk Test</button>
                <button class="btn btn-success" onclick="bulkSimulate()">📈 Bulk Simulate Orders</button>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Processing request...</p>
            </div>
            
            <div id="result"></div>
        </div>
    </div>

    <script>
        const baseUrl = '${baseUrl}';
        let stats = {
            testsRun: 0,
            successfulTests: 0,
            failedTests: 0,
            simulatedOrders: 0
        };

        function updateStats() {
            document.getElementById('testsRun').textContent = stats.testsRun;
            document.getElementById('successfulTests').textContent = stats.successfulTests;
            document.getElementById('failedTests').textContent = stats.failedTests;
            document.getElementById('simulatedOrders').textContent = stats.simulatedOrders;
        }

        function setCode(code) {
            document.getElementById('testCode').value = code;
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').innerHTML = '';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showResult(data, type = 'info') {
            hideLoading();
            const resultDiv = document.getElementById('result');
            resultDiv.className = \`result \${type}\`;
            resultDiv.textContent = JSON.stringify(data, null, 2);
            
            stats.testsRun++;
            if (data.success) {
                stats.successfulTests++;
            } else {
                stats.failedTests++;
            }
            updateStats();
        }

        async function testCode() {
            const code = document.getElementById('testCode').value.trim();
            const shop = document.getElementById('shopDomain').value.trim();
            
            if (!code) {
                alert('Please enter an invite code');
                return;
            }

            showLoading();
            
            try {
                const url = new URL(\`\${baseUrl}/api/test-invite-code\`);
                url.searchParams.set('code', code);
                if (shop) url.searchParams.set('shop', shop);
                
                const response = await fetch(url);
                const data = await response.json();
                
                showResult(data, data.success ? 'success' : 'error');
            } catch (error) {
                showResult({ error: error.message }, 'error');
            }
        }

        async function simulateUsage() {
            const code = document.getElementById('testCode').value.trim();
            const shop = document.getElementById('shopDomain').value.trim();
            
            if (!code) {
                alert('Please enter an invite code');
                return;
            }

            showLoading();
            
            try {
                const response = await fetch(\`\${baseUrl}/api/test-invite-code\`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        code,
                        shop,
                        simulate: true,
                        metadata: {
                            customerId: \`test-customer-\${Date.now()}\`,
                            ipAddress: '*************',
                            userAgent: 'Test Interface'
                        }
                    })
                });
                
                const data = await response.json();
                showResult(data, data.success ? 'success' : 'error');
            } catch (error) {
                showResult({ error: error.message }, 'error');
            }
        }

        async function actualUsage() {
            const code = document.getElementById('testCode').value.trim();
            const shop = document.getElementById('shopDomain').value.trim();
            
            if (!code) {
                alert('Please enter an invite code');
                return;
            }

            if (!confirm('This will actually use the invite code. Continue?')) {
                return;
            }

            showLoading();
            
            try {
                const response = await fetch(\`\${baseUrl}/api/test-invite-code\`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        code,
                        shop,
                        simulate: false,
                        metadata: {
                            customerId: \`real-customer-\${Date.now()}\`,
                            ipAddress: '*************',
                            userAgent: 'Test Interface - Real Usage'
                        }
                    })
                });
                
                const data = await response.json();
                showResult(data, data.success ? 'success' : 'error');
            } catch (error) {
                showResult({ error: error.message }, 'error');
            }
        }

        async function simulateOrder() {
            const code = document.getElementById('testCode').value.trim();

            if (!code) {
                alert('Please enter an invite code');
                return;
            }

            showLoading();

            try {
                // Use the order simulation endpoint
                const response = await fetch(\`\${baseUrl}/api/simulate-order\`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        inviteCode: code,
                        simulateApplication: true,
                        simulatePurchase: true,
                        orderValue: Math.floor(Math.random() * 200 + 50),
                        customerId: \`sim-customer-\${Date.now()}\`
                    })
                });

                const data = await response.json();

                if (data.success) {
                    stats.simulatedOrders++;
                    updateStats();
                }

                showResult(data, data.success ? 'success' : 'error');

            } catch (error) {
                showResult({ error: error.message }, 'error');
            }
        }

        async function bulkTest() {
            const codes = document.getElementById('bulkCodes').value.trim().split('\\n').filter(c => c.trim());
            const shop = document.getElementById('shopDomain').value.trim();
            
            if (codes.length === 0) {
                alert('Please enter some invite codes');
                return;
            }

            showLoading();
            
            const results = [];
            
            for (const code of codes) {
                try {
                    const url = new URL(\`\${baseUrl}/api/test-invite-code\`);
                    url.searchParams.set('code', code.trim());
                    if (shop) url.searchParams.set('shop', shop);
                    
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    results.push({ code: code.trim(), result: data });
                    
                    stats.testsRun++;
                    if (data.success) {
                        stats.successfulTests++;
                    } else {
                        stats.failedTests++;
                    }
                } catch (error) {
                    results.push({ code: code.trim(), error: error.message });
                    stats.testsRun++;
                    stats.failedTests++;
                }
            }
            
            updateStats();
            showResult({ bulkTestResults: results }, 'info');
        }

        async function bulkSimulate() {
            const codes = document.getElementById('bulkCodes').value.trim().split('\\n').filter(c => c.trim());

            if (codes.length === 0) {
                alert('Please enter some invite codes');
                return;
            }

            if (!confirm(\`This will simulate orders for \${codes.length} codes. Continue?\`)) {
                return;
            }

            showLoading();

            const results = [];

            for (const code of codes) {
                try {
                    // Use the order simulation endpoint for each code
                    const response = await fetch(\`\${baseUrl}/api/simulate-order\`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            inviteCode: code.trim(),
                            simulateApplication: true,
                            simulatePurchase: true,
                            orderValue: Math.floor(Math.random() * 300 + 25),
                            customerId: \`bulk-customer-\${Date.now()}-\${Math.floor(Math.random() * 1000)}\`
                        })
                    });

                    const data = await response.json();
                    results.push({ code: code.trim(), result: data });

                    if (data.success) {
                        stats.simulatedOrders++;
                    }

                    // Small delay to avoid overwhelming the server
                    await new Promise(resolve => setTimeout(resolve, 200));

                } catch (error) {
                    results.push({ code: code.trim(), error: error.message });
                }
            }

            updateStats();
            showResult({
                success: true,
                message: \`Bulk order simulation completed for \${codes.length} codes\`,
                results: results,
                note: 'Check your analytics dashboard for updated conversion data'
            }, 'success');
        }

        // Initialize stats display
        updateStats();
    </script>
</body>
</html>`;

  return new Response(html, {
    headers: {
      "Content-Type": "text/html",
    },
  });
};
