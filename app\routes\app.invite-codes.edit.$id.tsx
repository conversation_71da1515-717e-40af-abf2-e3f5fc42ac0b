import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { useActionData, useLoaderData, useNavigation, useSubmit } from "@remix-run/react";
import { Page, Layout, Card, Text, Box, Banner, InlineStack } from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import { getInviteCodeById, updateInviteCode } from "../models/invite-code.server";
import { getPartnersForDropdown } from "../models/partner.server";
import { getMembershipTagsForDropdown } from "../models/membership-tag.server";
import { InviteCodeForm, type InviteCodeFormData } from "../components/InviteCodeForm";
import { ThemeToggle } from "../components/UI/ThemeToggle";
import { formatDate, formatDateTime } from "../utils/dateFormatter";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  const id = parseInt(params.id!);

  console.log(`🔍 Loading invite code ${id} for editing...`);

  try {
    const [inviteCode, partners, membershipTags] = await Promise.all([
      getInviteCodeById(id, shop),
      getPartnersForDropdown(shop),
      getMembershipTagsForDropdown(shop),
    ]);

    if (!inviteCode) {
      throw new Response("Invite code not found", { status: 404 });
    }

    console.log(`✅ Found invite code: ${inviteCode.code}`);

    return json({
      inviteCode,
      shop,
      partners,
      membershipTags,
    });
  } catch (error) {
    console.error("❌ Error loading invite code:", error);
    throw new Response("Invite code not found", { status: 404 });
  }
};

export const action = async ({ request, params }: ActionFunctionArgs) => {
  console.log("💾 Updating invite code...");
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  const id = parseInt(params.id!);

  try {
    const formData = await request.formData();
    const data = Object.fromEntries(formData) as any;
    console.log("📝 Form data received:", data);
    console.log("🏪 Shop:", shop);
    console.log("🆔 Code ID:", id);

    // Parse the form data
    const updateData: any = {};

    if (data.code && data.code.trim()) {
      updateData.code = data.code.trim().toUpperCase();
    }

    if (data.maxUses) {
      updateData.maxUses = parseInt(data.maxUses);
    }

    if (data.expiresAt) {
      updateData.expiresAt = new Date(data.expiresAt);
    }

    if (data.isActive !== undefined) {
      updateData.isActive = data.isActive === "true";
    }

    if (data.partnerId) {
      updateData.partnerId = parseInt(data.partnerId);
    }

    if (data.membershipId) {
      updateData.membershipId = data.membershipId;
    }

    console.log("🚀 Updating invite code with data:", updateData);

    const updatedCode = await updateInviteCode(id, shop, updateData);

    console.log("✅ Successfully updated invite code:", updatedCode);

    // Redirect to the codes list with success message
    return redirect(`/app/invite-codes?updated=${updatedCode.code}`);
    
  } catch (error: any) {
    console.error("❌ Error updating invite code:", error);
    
    return json(
      { error: "Failed to update invite code. Please try again.", success: false },
      { status: 500 }
    );
  }
};

export default function EditInviteCode() {
  const { inviteCode, shop, partners, membershipTags } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const submit = useSubmit();
  const isSubmitting = navigation.state === "submitting";

  const handleSubmit = (data: InviteCodeFormData) => {
    console.log("🚀 Edit form submission triggered with data:", data);
    console.log("🆔 Editing invite code ID:", inviteCode.id);

    // Use Remix's submit function for proper form submission
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, value?.toString() || "");
      console.log(`📝 Adding field ${key}:`, value);
    });

    console.log("📤 Submitting edit form with fields:", Object.keys(data));
    submit(formData, { method: "POST" });
  };

  const handleCancel = () => {
    window.history.back();
  };

  // Convert invite code data to form data
  const initialData: InviteCodeFormData = {
    code: inviteCode.code,
    maxUses: inviteCode.maxUses,
    expiresAt: inviteCode.expiresAt ? new Date(inviteCode.expiresAt).toISOString().split('T')[0] : "",
    isActive: inviteCode.isActive,
    partnerId: inviteCode.partnerId,
    membershipId: inviteCode.membershipId,
  };

  return (
    <Page
      title={`Edit Invite Code: ${inviteCode.code}`}
      subtitle="Update the settings for this invite code"
      backAction={{
        content: "Back to Invite Codes",
        url: "/app/invite-codes",
      }}
    >
      <Box paddingBlockEnd="400">
        <InlineStack align="end">
          <ThemeToggle />
        </InlineStack>
      </Box>

      <Layout>
        <Layout.Section variant="oneHalf">
          <InviteCodeForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={isSubmitting}
            error={actionData?.error}
            mode="edit"
            initialData={initialData}
            membershipPlans={membershipTags}
            partners={partners}
          />
        </Layout.Section>

        <Layout.Section variant="oneHalf">
          <Card>
            <Text variant="headingMd" as="h2">
              Usage Statistics
            </Text>
            <Box paddingBlockStart="400">
              <Text as="p">
                <strong>Times Used:</strong> {inviteCode.usages?.length || 0} / {inviteCode.maxUses}
              </Text>
              <Text as="p">
                <strong>Status:</strong> {inviteCode.isActive ? "Active" : "Inactive"}
              </Text>
              {inviteCode.expiresAt && (
                <Text as="p">
                  <strong>Expires:</strong> {formatDate(inviteCode.expiresAt)}
                </Text>
              )}
              <Text as="p">
                <strong>Created:</strong> {formatDate(inviteCode.createdAt)}
              </Text>
            </Box>
          </Card>

          {inviteCode.usages && inviteCode.usages.length > 0 && (
            <Box paddingBlockStart="400">
              <Card>
                <Text variant="headingMd" as="h2">
                  Recent Usage
                </Text>
                <Box paddingBlockStart="400">
                  {inviteCode.usages.slice(0, 5).map((usage: any) => (
                    <Box key={usage.id} paddingBlockEnd="200">
                      <Text as="p" variant="bodySm">
                        {formatDateTime(usage.usedAt)}
                        {usage.customerId && ` - Customer: ${usage.customerId}`}
                      </Text>
                    </Box>
                  ))}
                </Box>
              </Card>
            </Box>
          )}
        </Layout.Section>
      </Layout>
    </Page>
  );
}
