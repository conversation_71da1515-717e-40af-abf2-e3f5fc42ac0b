import {
  Card,
  Text,
  Box,
  BlockStack,
  InlineStack,
  Badge,
  EmptyState,
  Avatar,
} from "@shopify/polaris";

interface RecentActivityProps {
  recentConversions: Array<{
    code: string;
    partnerName: string | null;
    usedAt: Date;
    converted: boolean;
    customerId: string | null;
  }>;
}

export function RecentActivity({ recentConversions }: RecentActivityProps) {
  if (recentConversions.length === 0) {
    return (
      <Card>
        <EmptyState
          heading="No recent activity"
          image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
        >
          <p>Recent invite code usage and conversions will appear here.</p>
        </EmptyState>
      </Card>
    );
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
    }).format(date);
  };

  const getActivityIcon = (converted: boolean) => {
    return converted ? "🛒" : "👀";
  };

  const getActivityBadge = (converted: boolean) => {
    return converted 
      ? <Badge tone="success">Purchased</Badge>
      : <Badge tone="info">Applied</Badge>;
  };

  return (
    <Card>
      <Box padding="400">
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text variant="headingMd" as="h3">
              Recent Activity
            </Text>
            <Badge tone="info">
              {recentConversions.length} Recent
            </Badge>
          </InlineStack>
          
          <BlockStack gap="300">
            {recentConversions.map((activity, index) => (
              <Box
                key={`${activity.code}-${activity.usedAt.getTime()}-${index}`}
                padding="300"
                background="bg-surface-secondary"
                borderRadius="200"
              >
                <InlineStack gap="300" align="start">
                  <Box>
                    <Avatar
                      size="small"
                      name={activity.partnerName || "Unknown"}
                      initials={activity.partnerName ? activity.partnerName.substring(0, 2).toUpperCase() : "?"}
                    />
                  </Box>
                  
                  <Box style={{ flex: 1 }}>
                    <BlockStack gap="200">
                      <InlineStack gap="200" align="start">
                        <Text variant="bodyMd" fontWeight="medium">
                          {activity.code}
                        </Text>
                        {getActivityBadge(activity.converted)}
                      </InlineStack>
                      
                      <Text variant="bodySm" tone="subdued">
                        {activity.partnerName ? `Partner: ${activity.partnerName}` : "No partner assigned"}
                      </Text>
                      
                      {activity.customerId && (
                        <Text variant="bodySm" tone="subdued">
                          Customer ID: {activity.customerId}
                        </Text>
                      )}
                      
                      <InlineStack gap="200" align="start">
                        <Text variant="bodySm" tone="subdued">
                          {formatTimeAgo(activity.usedAt)}
                        </Text>
                        <Text variant="bodySm">
                          {getActivityIcon(activity.converted)} {activity.converted ? "Converted to purchase" : "Code applied"}
                        </Text>
                      </InlineStack>
                    </BlockStack>
                  </Box>
                </InlineStack>
              </Box>
            ))}
          </BlockStack>
          
          {recentConversions.length >= 10 && (
            <Box paddingBlockStart="300">
              <Text variant="bodySm" tone="subdued" alignment="center">
                Showing last 10 activities. View all codes for complete history.
              </Text>
            </Box>
          )}
        </BlockStack>
      </Box>
    </Card>
  );
}
