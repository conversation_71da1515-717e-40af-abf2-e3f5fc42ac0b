/**
 * CORS utility for handling cross-origin requests
 * Useful for public API endpoints that need to be accessible from external domains
 */

export function cors(request: Request, response: Response): Response {
  const origin = request.headers.get("origin");
  
  // Allow all origins for testing endpoints
  // In production, you might want to restrict this to specific domains
  const allowedOrigins = [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://127.0.0.1:3000",
    "https://127.0.0.1:3000",
    // Add your production domains here
  ];

  // For development and testing, allow all origins
  const isDevelopment = process.env.NODE_ENV === "development";
  const shouldAllowOrigin = isDevelopment || !origin || allowedOrigins.includes(origin);

  if (shouldAllowOrigin) {
    response.headers.set("Access-Control-Allow-Origin", origin || "*");
  }

  response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
  response.headers.set("Access-Control-Allow-Credentials", "false");
  response.headers.set("Access-Control-Max-Age", "86400"); // 24 hours

  return response;
}
