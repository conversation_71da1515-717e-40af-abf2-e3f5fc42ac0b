/*
  Warnings:

  - You are about to drop the column `used_count` on the `invite_codes` table. All the data in the column will be lost.
  - Added the required column `membership_id` to the `invite_codes` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
-- First, preserve the used_count data by copying it to applications_count
ALTER TABLE "invite_codes" ADD COLUMN "applications_count" INTEGER NOT NULL DEFAULT 0;
UPDATE "invite_codes" SET "applications_count" = "used_count";

-- Add other new columns with defaults for existing data
ALTER TABLE "invite_codes" ADD COLUMN "purchases_count" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "invite_codes" ADD COLUMN "partner_id" INTEGER;

-- Add membership_id as nullable first, then set default values for existing records
ALTER TABLE "invite_codes" ADD COLUMN "membership_id" VARCHAR(255);

-- Set a default membership_id for existing records (using a default plan)
UPDATE "invite_codes" SET "membership_id" = 'basic-monthly' WHERE "membership_id" IS NULL;

-- Now make membership_id required
ALTER TABLE "invite_codes" ALTER COLUMN "membership_id" SET NOT NULL;

-- Finally, drop the old used_count column
ALTER TABLE "invite_codes" DROP COLUMN "used_count";

-- CreateTable
CREATE TABLE "partners" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "email" VARCHAR(255),
    "phone" VARCHAR(50),
    "company" VARCHAR(255),
    "notes" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,
    "shop_id" VARCHAR(255) NOT NULL,

    CONSTRAINT "partners_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "partners_shop_id_idx" ON "partners"("shop_id");

-- CreateIndex
CREATE INDEX "partners_name_idx" ON "partners"("name");

-- CreateIndex
CREATE INDEX "invite_codes_partner_id_idx" ON "invite_codes"("partner_id");

-- CreateIndex
CREATE INDEX "invite_codes_membership_id_idx" ON "invite_codes"("membership_id");

-- AddForeignKey
ALTER TABLE "invite_codes" ADD CONSTRAINT "invite_codes_partner_id_fkey" FOREIGN KEY ("partner_id") REFERENCES "partners"("id") ON DELETE SET NULL ON UPDATE CASCADE;
