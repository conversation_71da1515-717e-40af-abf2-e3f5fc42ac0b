import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { createInviteCode } from "../models/invite-code.server";
import { createPartner } from "../models/partner.server";

/**
 * Generate test invite codes for analytics testing
 * This endpoint creates sample invite codes with different partners for testing
 */

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    return json({
      success: false,
      error: "Method not allowed. Use POST to generate test codes.",
    }, { status: 405 });
  }

  try {
    const { session } = await authenticate.admin(request);
    const shop = session.shop;

    const body = await request.json();
    const { count = 10, createPartners = true } = body;

    console.log(`🧪 Generating ${count} test invite codes for shop: ${shop}`);

    const results = {
      shop,
      partnersCreated: [],
      codesCreated: [],
      errors: [],
    };

    // Sample partner data
    const samplePartners = [
      { name: "Alpha Marketing", email: "<EMAIL>", company: "Alpha Marketing LLC" },
      { name: "Beta Promotions", email: "<EMAIL>", company: "Beta Promotions Inc" },
      { name: "Gamma Affiliates", email: "<EMAIL>", company: "Gamma Affiliates" },
      { name: "Delta Influencers", email: "<EMAIL>", company: "Delta Influencer Network" },
      { name: "Epsilon Partners", email: "<EMAIL>", company: "Epsilon Partnership Group" },
    ];

    // Sample membership IDs
    const membershipIds = ["premium", "basic", "vip", "standard", "gold", "silver", "bronze"];

    // Create test partners if requested
    let partnerIds = [];
    if (createPartners) {
      for (const partnerData of samplePartners) {
        try {
          const partner = await createPartner({
            ...partnerData,
            shopId: shop,
          });
          results.partnersCreated.push(partner);
          partnerIds.push(partner.id);
          console.log(`✅ Created partner: ${partner.name} (ID: ${partner.id})`);
        } catch (error) {
          console.error(`❌ Error creating partner ${partnerData.name}:`, error);
          results.errors.push(`Failed to create partner ${partnerData.name}: ${error.message}`);
        }
      }
    }

    // Generate test invite codes
    const codeNames = [
      "WELCOME10", "SAVE20", "NEWUSER", "PREMIUM", "DISCOUNT15", 
      "SPECIAL25", "FIRST30", "BONUS40", "MEGA50", "SUPER60",
      "ALPHA10", "BETA20", "GAMMA30", "DELTA40", "EPSILON50",
      "FLASH15", "QUICK25", "FAST35", "RAPID45", "SWIFT55"
    ];

    for (let i = 0; i < Math.min(count, codeNames.length); i++) {
      try {
        const code = codeNames[i];
        const partnerId = partnerIds.length > 0 ? partnerIds[i % partnerIds.length] : null;
        const membershipId = membershipIds[i % membershipIds.length];
        const maxUses = Math.floor(Math.random() * 100) + 10; // 10-110 uses
        
        // Random expiry date (some expired, some future)
        const daysFromNow = Math.floor(Math.random() * 120) - 30; // -30 to +90 days
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + daysFromNow);

        const inviteCode = await createInviteCode({
          code,
          maxUses,
          expiresAt: daysFromNow > 0 ? expiresAt : undefined, // Only set expiry for future dates
          createdBy: "test-generator",
          shopId: shop,
          partnerId,
          membershipId,
        });

        results.codesCreated.push({
          id: inviteCode.id,
          code: inviteCode.code,
          partnerId: inviteCode.partnerId,
          membershipId: inviteCode.membershipId,
          maxUses: inviteCode.maxUses,
          expiresAt: inviteCode.expiresAt,
        });

        console.log(`✅ Created invite code: ${code} (ID: ${inviteCode.id})`);

      } catch (error) {
        console.error(`❌ Error creating invite code ${codeNames[i]}:`, error);
        results.errors.push(`Failed to create code ${codeNames[i]}: ${error.message}`);
      }
    }

    return json({
      success: true,
      message: `Generated ${results.codesCreated.length} test invite codes`,
      results,
      summary: {
        partnersCreated: results.partnersCreated.length,
        codesCreated: results.codesCreated.length,
        errors: results.errors.length,
      },
      note: "You can now test these codes using the testing interface at /test-interface"
    });

  } catch (error) {
    console.error("❌ Error in test code generation endpoint:", error);
    return json({
      success: false,
      error: "Internal server error during test code generation",
      details: error.message,
    }, { status: 500 });
  }
};

export const loader = async ({ request }: ActionFunctionArgs) => {
  return json({
    success: false,
    error: "Use POST to generate test codes",
    usage: "POST /api/generate-test-codes with { count: 10, createPartners: true }",
    example: {
      method: "POST",
      body: {
        count: 10,
        createPartners: true
      }
    }
  }, { status: 405 });
};
