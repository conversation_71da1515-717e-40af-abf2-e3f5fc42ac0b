import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Seeding database with development data...");

  // Development environment configuration
  const isDevelopment = process.env.NODE_ENV === "development" || !process.env.NODE_ENV;

  // Development shops and users
  const developmentShops = [
    {
      shopId: "supersleek-staging.myshopify.com",
      createdBy: "<EMAIL>",
      name: "Supersleek Staging"
    },
    {
      shopId: "supersleek-test.myshopify.com",
      createdBy: "<EMAIL>",
      name: "Supersleek Test"
    },
    {
      shopId: "supersleek-demo.myshopify.com",
      createdBy: "<EMAIL>",
      name: "Supersleek Demo"
    }
  ];

  // Use first shop as default for development
  const defaultShop = developmentShops[0];
  const shopId = defaultShop.shopId;
  const createdBy = defaultShop.createdBy;

  // Comprehensive development invite codes
  const inviteCodes = [
    // Active promotional codes
    {
      code: "WELCOME10",
      maxUses: 100,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      createdBy,
      shopId,
      isActive: true,
    },
    {
      code: "NEWUSER25",
      maxUses: 50,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      createdBy,
      shopId,
      isActive: true,
    },
    {
      code: "FLASH50",
      maxUses: 20,
      expiresAt: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 day from now
      createdBy,
      shopId,
      isActive: true,
    },
    {
      code: "UNLIMITED",
      maxUses: 999999,
      expiresAt: null, // No expiry
      createdBy,
      shopId,
      isActive: true,
    },
    // Test cases
    {
      code: "EXPIRED",
      maxUses: 10,
      expiresAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // Expired yesterday
      createdBy,
      shopId,
      isActive: true,
    },
    {
      code: "SINGLEUSE",
      maxUses: 1,
      expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
      createdBy,
      shopId,
      isActive: true,
    },
    {
      code: "INACTIVE",
      maxUses: 50,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      createdBy,
      shopId,
      isActive: false, // Inactive for testing
    },
    // Development-specific codes
    {
      code: "DEVTEST",
      maxUses: 999,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      createdBy,
      shopId,
      isActive: true,
    },
    {
      code: "APITEST",
      maxUses: 100,
      expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
      createdBy,
      shopId,
      isActive: true,
    },
    {
      code: "DEMO2024",
      maxUses: 25,
      expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days
      createdBy,
      shopId,
      isActive: true,
    },
  ];

  // Additional codes for other development shops
  const additionalCodes = [];
  if (isDevelopment) {
    for (let i = 1; i < developmentShops.length; i++) {
      const shop = developmentShops[i];
      additionalCodes.push(
        {
          code: `${shop.name.toUpperCase().replace(/\s+/g, '')}10`,
          maxUses: 50,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          createdBy: shop.createdBy,
          shopId: shop.shopId,
          isActive: true,
        },
        {
          code: `${shop.name.toUpperCase().replace(/\s+/g, '')}TEST`,
          maxUses: 10,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          createdBy: shop.createdBy,
          shopId: shop.shopId,
          isActive: true,
        }
      );
    }
  }

  const allCodes = [...inviteCodes, ...additionalCodes];

  // Create invite codes with comprehensive usage data
  const createdCodes = [];

  for (const codeData of allCodes) {
    const inviteCode = await prisma.inviteCode.upsert({
      where: { code: codeData.code },
      update: {},
      create: codeData,
    });

    createdCodes.push(inviteCode);
    console.log(`✅ Created invite code: ${inviteCode.code} (Shop: ${inviteCode.shopId})`);
  }

  // Add comprehensive sample usage data
  const sampleCustomers = [
    { id: "customer_001", email: "<EMAIL>", name: "John Doe" },
    { id: "customer_002", email: "<EMAIL>", name: "Jane Smith" },
    { id: "customer_003", email: "<EMAIL>", name: "Bob Wilson" },
    { id: "customer_004", email: "<EMAIL>", name: "Alice Brown" },
    { id: "customer_005", email: "<EMAIL>", name: "Charlie Davis" },
    { id: "customer_006", email: "<EMAIL>", name: "Diana Miller" },
    { id: "customer_007", email: "<EMAIL>", name: "Evan Garcia" },
    { id: "customer_008", email: "<EMAIL>", name: "Fiona Martinez" },
  ];

  const userAgents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15",
    "Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0",
    "Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15",
  ];

  // Add realistic usage patterns
  const usagePatterns = [
    { code: "WELCOME10", usages: 15, days: 10 },
    { code: "NEWUSER25", usages: 8, days: 5 },
    { code: "FLASH50", usages: 3, days: 1 },
    { code: "DEVTEST", usages: 25, days: 30 },
    { code: "APITEST", usages: 12, days: 7 },
    { code: "DEMO2024", usages: 5, days: 14 },
    { code: "SINGLEUSE", usages: 1, days: 2 },
  ];

  for (const pattern of usagePatterns) {
    const inviteCode = createdCodes.find(code => code.code === pattern.code);
    if (!inviteCode) continue;

    let usageCount = 0;
    for (let i = 0; i < pattern.usages; i++) {
      const customer = sampleCustomers[i % sampleCustomers.length];
      const userAgent = userAgents[i % userAgents.length];
      const daysAgo = Math.floor(Math.random() * pattern.days);
      const hoursAgo = Math.floor(Math.random() * 24);
      const usedAt = new Date(Date.now() - (daysAgo * 24 + hoursAgo) * 60 * 60 * 1000);

      await prisma.usage.create({
        data: {
          inviteCodeId: inviteCode.id,
          customerId: customer.id,
          ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
          userAgent,
          usedAt,
        },
      });
      usageCount++;
    }

    // Update used count
    await prisma.inviteCode.update({
      where: { id: inviteCode.id },
      data: { usedCount: usageCount },
    });

    console.log(`  📊 Added ${usageCount} sample usages for ${pattern.code}`);
  }

  // Summary
  const totalCodes = createdCodes.length;
  const totalUsages = await prisma.usage.count();
  const activeShops = new Set(createdCodes.map(code => code.shopId)).size;

  console.log("\n🎉 Database seeded successfully!");
  console.log("📈 Summary:");
  console.log(`  • ${totalCodes} invite codes created`);
  console.log(`  • ${totalUsages} usage records created`);
  console.log(`  • ${activeShops} development shops configured`);
  console.log(`  • Environment: ${isDevelopment ? 'Development' : 'Production'}`);

  if (isDevelopment) {
    console.log("\n🔧 Development URLs:");
    console.log("  • pgAdmin: http://localhost:8080");
    console.log("  • API Docs: http://localhost:3000/api/docs");
    console.log("  • Validation: http://localhost:3000/validate");
    console.log("\n👤 Development Credentials:");
    console.log("  • pgAdmin: <EMAIL> / dev123456");
    console.log("  • Database: invite_app / dev_password_2024");
  }
}

main()
  .catch((e) => {
    console.error("❌ Error seeding database:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
