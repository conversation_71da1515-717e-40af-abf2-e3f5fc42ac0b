import type { LoaderFunctionArgs } from "@remix-run/node";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const baseUrl = new URL(request.url).origin;
  
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invite Code Testing API Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        h3 { color: #7f8c8d; }
        .endpoint { 
            background: #ecf0f1; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }
        .method { 
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
            margin-right: 10px;
        }
        .get { background: #27ae60; }
        .post { background: #e74c3c; }
        code { 
            background: #f1c40f; 
            padding: 2px 6px; 
            border-radius: 3px;
            color: #2c3e50;
        }
        pre { 
            background: #2c3e50; 
            color: #ecf0f1; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto;
        }
        .example {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffc107;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .response-example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Invite Code Testing API</h1>
        <p>This API allows you to test actual invite codes created in your Supertested app without requiring authentication. Perfect for external testing, validation, and integration testing.</p>

        <div class="warning">
            <strong>⚠️ Important:</strong> This is a testing endpoint. Use the <code>simulate=true</code> parameter to test without actually consuming invite code uses.
        </div>

        <h2>Base URL</h2>
        <div class="endpoint">
            <code>${baseUrl}/api/test-invite-code</code>
        </div>

        <h2>Endpoints</h2>

        <h3>1. Test Invite Code (GET)</h3>
        <div class="endpoint">
            <span class="method get">GET</span>
            <code>/api/test-invite-code?code=YOUR_CODE&shop=your-shop.myshopify.com</code>
        </div>
        <p>Tests an invite code without using it. Returns detailed information about the code's validity and current status.</p>

        <h4>Query Parameters</h4>
        <table>
            <tr><th>Parameter</th><th>Required</th><th>Description</th></tr>
            <tr><td><code>code</code></td><td>Yes</td><td>The invite code to test</td></tr>
            <tr><td><code>shop</code></td><td>No</td><td>Shop domain to verify code belongs to specific shop</td></tr>
        </table>

        <div class="example">
            <h4>Example Request:</h4>
            <pre>GET ${baseUrl}/api/test-invite-code?code=WELCOME10&shop=supertested-staging.myshopify.com</pre>
        </div>

        <div class="response-example">
            <h4>Example Response:</h4>
            <pre>{
  "success": true,
  "valid": true,
  "code": "WELCOME10",
  "details": {
    "id": 1,
    "shopId": "supertested-staging.myshopify.com",
    "isActive": true,
    "maxUses": 100,
    "currentUsage": 5,
    "remainingUses": 95,
    "expiresAt": "2024-12-31T23:59:59.000Z",
    "expired": false,
    "membershipId": "premium",
    "partnerId": 1,
    "applicationsCount": 5,
    "purchasesCount": 2,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "createdBy": "<EMAIL>"
  },
  "reasons": {
    "inactive": false,
    "expired": false,
    "maxUsesReached": false
  },
  "timestamp": "2024-07-29T12:00:00.000Z"
}</pre>
        </div>

        <h3>2. Test and Simulate Usage (POST)</h3>
        <div class="endpoint">
            <span class="method post">POST</span>
            <code>/api/test-invite-code</code>
        </div>
        <p>Test an invite code with option to simulate usage or actually use it.</p>

        <h4>Request Body</h4>
        <table>
            <tr><th>Field</th><th>Required</th><th>Description</th></tr>
            <tr><td><code>code</code></td><td>Yes</td><td>The invite code to test</td></tr>
            <tr><td><code>shop</code></td><td>No</td><td>Shop domain for verification</td></tr>
            <tr><td><code>simulate</code></td><td>No</td><td>If true, simulates usage without recording it (default: false)</td></tr>
            <tr><td><code>metadata</code></td><td>No</td><td>Additional data (customerId, ipAddress, userAgent)</td></tr>
        </table>

        <div class="example">
            <h4>Example Request (Simulation):</h4>
            <pre>POST ${baseUrl}/api/test-invite-code
Content-Type: application/json

{
  "code": "WELCOME10",
  "shop": "supertested-staging.myshopify.com",
  "simulate": true,
  "metadata": {
    "customerId": "test-customer-123",
    "ipAddress": "***********"
  }
}</pre>
        </div>

        <div class="response-example">
            <h4>Example Response (Simulation):</h4>
            <pre>{
  "success": true,
  "simulation": true,
  "wouldBeValid": true,
  "code": "WELCOME10",
  "details": {
    "shopId": "supertested-staging.myshopify.com",
    "membershipId": "premium",
    "partnerId": 1,
    "currentUsage": 5,
    "maxUses": 100,
    "remainingUses": 95,
    "expiresAt": "2024-12-31T23:59:59.000Z"
  },
  "simulatedResult": "Code would be accepted and usage recorded",
  "reasons": {
    "inactive": false,
    "expired": false,
    "maxUsesReached": false
  },
  "timestamp": "2024-07-29T12:00:00.000Z"
}</pre>
        </div>

        <h2>Error Responses</h2>
        <p>All error responses follow this format:</p>
        <div class="response-example">
            <pre>{
  "success": false,
  "error": "Error description",
  "code": "ATTEMPTED_CODE",
  "timestamp": "2024-07-29T12:00:00.000Z"
}</pre>
        </div>

        <h3>Common Error Codes</h3>
        <table>
            <tr><th>HTTP Status</th><th>Error</th><th>Description</th></tr>
            <tr><td>400</td><td>Missing required parameter: code</td><td>Code parameter not provided</td></tr>
            <tr><td>404</td><td>Invite code not found</td><td>Code doesn't exist in database</td></tr>
            <tr><td>400</td><td>Invite code does not belong to the specified shop</td><td>Code exists but for different shop</td></tr>
            <tr><td>400</td><td>Code has expired</td><td>Code is past expiration date</td></tr>
            <tr><td>400</td><td>Code has reached maximum uses</td><td>Code has been used up</td></tr>
            <tr><td>400</td><td>Code is inactive</td><td>Code has been deactivated</td></tr>
        </table>

        <h2>Testing Examples</h2>

        <h3>Using cURL</h3>
        <div class="example">
            <h4>Test without using:</h4>
            <pre>curl "${baseUrl}/api/test-invite-code?code=WELCOME10"</pre>
            
            <h4>Simulate usage:</h4>
            <pre>curl -X POST "${baseUrl}/api/test-invite-code" \\
  -H "Content-Type: application/json" \\
  -d '{"code":"WELCOME10","simulate":true}'</pre>
        </div>

        <h3>Using JavaScript</h3>
        <div class="example">
            <pre>// Test code validity
const response = await fetch('${baseUrl}/api/test-invite-code?code=WELCOME10');
const result = await response.json();
console.log(result);

// Simulate usage
const simulateResponse = await fetch('${baseUrl}/api/test-invite-code', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    code: 'WELCOME10',
    simulate: true,
    metadata: { customerId: 'test-123' }
  })
});
const simulateResult = await simulateResponse.json();
console.log(simulateResult);</pre>
        </div>

        <h2>Integration Notes</h2>
        <ul>
            <li><strong>CORS:</strong> This endpoint supports CORS for cross-origin requests</li>
            <li><strong>Rate Limiting:</strong> No rate limiting currently implemented</li>
            <li><strong>Authentication:</strong> No authentication required for testing</li>
            <li><strong>Simulation Mode:</strong> Always use <code>simulate: true</code> for testing to avoid consuming actual uses</li>
            <li><strong>Real Usage:</strong> Only use <code>simulate: false</code> when you want to actually record usage</li>
        </ul>

        <div class="warning">
            <strong>🔒 Security Note:</strong> This endpoint is designed for testing. In production, consider implementing rate limiting and monitoring to prevent abuse.
        </div>

        <h2>Related Endpoints</h2>
        <ul>
            <li><a href="${baseUrl}/api/docs">Main API Documentation</a></li>
            <li><a href="${baseUrl}/api/validate-invite">Production Validation Endpoint</a></li>
        </ul>
    </div>
</body>
</html>`;

  return new Response(html, {
    headers: {
      "Content-Type": "text/html",
    },
  });
};
