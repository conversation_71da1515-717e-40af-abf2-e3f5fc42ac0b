import prisma from "../db.server";

export interface AppSettings {
  id: number;
  shop: string;
  dateFormat: string;
  timeFormat: string;
  timezone: string;
  defaultExpiryDays: number | null;
  enableEmailNotifications: boolean;
  maxCodesPerBatch: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface AppSettingsData {
  dateFormat: string;
  timeFormat: string;
  timezone: string;
  defaultExpiryDays: number | null;
  enableEmailNotifications: boolean;
  maxCodesPerBatch: number;
}

// Default settings
export const DEFAULT_SETTINGS: AppSettingsData = {
  dateFormat: "YYYY-MM-DD",
  timeFormat: "24h",
  timezone: "UTC",
  defaultExpiryDays: null,
  enableEmailNotifications: true,
  maxCodesPerBatch: 100,
};

/**
 * Get app settings for a shop
 */
export async function getAppSettings(shop: string): Promise<AppSettings> {
  console.log(`⚙️ Getting app settings for shop: ${shop}`);
  
  try {
    let settings = await prisma.appSettings.findFirst({
      where: { shop },
    });

    // If no settings exist, create default settings
    if (!settings) {
      console.log(`📝 Creating default settings for shop: ${shop}`);
      settings = await prisma.appSettings.create({
        data: {
          shop,
          ...DEFAULT_SETTINGS,
        },
      });
    }

    console.log(`✅ Found app settings for shop: ${shop}`);
    return settings;
  } catch (error) {
    console.error(`❌ Error fetching app settings:`, error);
    throw new Error("Failed to fetch app settings");
  }
}

/**
 * Update app settings for a shop
 */
export async function updateAppSettings(
  shop: string,
  data: Partial<AppSettingsData>
): Promise<AppSettings> {
  console.log(`⚙️ Updating app settings for shop: ${shop}`, data);
  
  try {
    // First ensure settings exist
    await getAppSettings(shop);

    const updatedSettings = await prisma.appSettings.updateMany({
      where: { shop },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });

    if (updatedSettings.count === 0) {
      throw new Error("No settings found to update");
    }

    // Return the updated settings
    const settings = await getAppSettings(shop);
    console.log(`✅ Updated app settings for shop: ${shop}`);
    return settings;
  } catch (error) {
    console.error(`❌ Error updating app settings:`, error);
    throw new Error("Failed to update app settings");
  }
}

/**
 * Get date format options for dropdown
 */
export function getDateFormatOptions() {
  return [
    { label: "YYYY-MM-DD (2024-12-25)", value: "YYYY-MM-DD" },
    { label: "MM/DD/YYYY (12/25/2024)", value: "MM/DD/YYYY" },
    { label: "DD/MM/YYYY (25/12/2024)", value: "DD/MM/YYYY" },
    { label: "DD-MM-YYYY (25-12-2024)", value: "DD-MM-YYYY" },
  ];
}

/**
 * Get time format options for dropdown
 */
export function getTimeFormatOptions() {
  return [
    { label: "24 Hour (23:59)", value: "24h" },
    { label: "12 Hour (11:59 PM)", value: "12h" },
  ];
}

/**
 * Get timezone options for dropdown
 */
export function getTimezoneOptions() {
  return [
    { label: "UTC (Coordinated Universal Time)", value: "UTC" },
    { label: "EST (Eastern Standard Time)", value: "America/New_York" },
    { label: "PST (Pacific Standard Time)", value: "America/Los_Angeles" },
    { label: "CST (Central Standard Time)", value: "America/Chicago" },
    { label: "MST (Mountain Standard Time)", value: "America/Denver" },
    { label: "GMT (Greenwich Mean Time)", value: "Europe/London" },
    { label: "CET (Central European Time)", value: "Europe/Paris" },
    { label: "JST (Japan Standard Time)", value: "Asia/Tokyo" },
    { label: "AEST (Australian Eastern Standard Time)", value: "Australia/Sydney" },
  ];
}

/**
 * Format date according to settings
 */
export function formatDateBySettings(date: Date | string, settings: AppSettings): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return '';
  }

  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');

  switch (settings.dateFormat) {
    case "MM/DD/YYYY":
      return `${month}/${day}/${year}`;
    case "DD/MM/YYYY":
      return `${day}/${month}/${year}`;
    case "DD-MM-YYYY":
      return `${day}-${month}-${year}`;
    case "YYYY-MM-DD":
    default:
      return `${year}-${month}-${day}`;
  }
}

/**
 * Format time according to settings
 */
export function formatTimeBySettings(date: Date | string, settings: AppSettings): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return '';
  }

  if (settings.timeFormat === "12h") {
    return dateObj.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  } else {
    return dateObj.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  }
}

/**
 * Get input format for date fields based on settings
 */
export function getDateInputFormat(settings: AppSettings): string {
  // For HTML input fields, we always use YYYY-MM-DD format
  // But we can show different placeholder text
  return "YYYY-MM-DD";
}

/**
 * Get placeholder text for date inputs based on settings
 */
export function getDatePlaceholder(settings: AppSettings): string {
  const today = new Date();
  return formatDateBySettings(today, settings);
}
