-- Supertested Invite Code Database Initialization
-- This script sets up the initial database configuration

-- Create the database if it doesn't exist (handled by Docker environment)
-- CREATE DATABASE supertested_invite_codes;

-- Create user and grant permissions (handled by Docker environment)
-- CREATE USER invite_app WITH PASSWORD 'dev_password_2024';
-- GRANT ALL PRIVILEGES ON DATABASE supertested_invite_codes TO invite_app;

-- Connect to the database
\c supertested_invite_codes;

-- Create extensions that might be useful
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For better text search performance

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO invite_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO invite_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO invite_app;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO invite_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO invite_app;

-- Create a simple test to verify connection
CREATE TABLE IF NOT EXISTS connection_test (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    message TEXT DEFAULT 'Database connection successful!'
);

INSERT INTO connection_test (message) VALUES ('Initial setup completed successfully!');

-- Display success message
SELECT 'PostgreSQL database setup completed successfully!' as status;
