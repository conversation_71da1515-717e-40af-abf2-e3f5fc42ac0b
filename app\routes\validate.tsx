import { useState } from "react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const shop = url.searchParams.get("shop");

  return json({
    initialCode: code || "",
    shopDomain: shop || process.env.DEV_SHOP_DOMAIN || "supersleek-staging.myshopify.com",
    apiUrl: `${url.origin}/api/validate-invite`
  });
};

export default function ValidateInvitePage() {
  const { initialCode, shopDomain, apiUrl } = useLoaderData<typeof loader>();
  const [code, setCode] = useState(initialCode);
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleValidate = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setResult(null);

    try {
      // First validate the invite code
      const validateResponse = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: code.toUpperCase(),
          shopDomain,
        }),
      });

      const validateData = await validateResponse.json();

      if (!validateData.success) {
        setResult(validateData);
        return;
      }

      // If validation successful, create customer account
      const createCustomerResponse = await fetch("/api/create-customer", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          inviteCode: code.toUpperCase(),
          email: email || undefined,
          firstName: firstName || undefined,
          lastName: lastName || undefined,
          acceptsMarketing: false,
          returnTo: "/",
        }),
      });

      const customerData = await createCustomerResponse.json();

      if (customerData.success) {
        setResult({
          success: true,
          message: "Account created successfully! Redirecting to login...",
          data: {
            ...validateData.data,
            customer: customerData.customer,
          }
        });

        // Redirect to Shopify login page after a short delay
        setTimeout(() => {
          if (customerData.loginUrl) {
            window.location.href = customerData.loginUrl;
          }
        }, 2000);
      } else {
        setResult({
          success: false,
          message: customerData.error || "Failed to create customer account",
          error: "CUSTOMER_CREATION_ERROR",
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: "Failed to process invite code",
        error: "NETWORK_ERROR",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      minHeight: "100vh", 
      background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      padding: "20px"
    }}>
      <div style={{
        background: "white",
        borderRadius: "12px",
        padding: "40px",
        maxWidth: "500px",
        width: "100%",
        boxShadow: "0 20px 40px rgba(0,0,0,0.1)"
      }}>
        <div style={{ textAlign: "center", marginBottom: "30px" }}>
          <h1 style={{ 
            color: "#333", 
            fontSize: "28px", 
            marginBottom: "10px",
            fontWeight: "600"
          }}>
            Validate Invite Code
          </h1>
          <p style={{ 
            color: "#666", 
            fontSize: "16px",
            margin: "0"
          }}>
            Enter your invite code to access exclusive offers
          </p>
        </div>

        <form onSubmit={handleValidate}>
          <div style={{ marginBottom: "20px" }}>
            <label style={{ 
              display: "block", 
              marginBottom: "8px", 
              fontWeight: "500",
              color: "#333"
            }}>
              Invite Code *
            </label>
            <input
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value.toUpperCase())}
              placeholder="Enter your invite code"
              required
              style={{
                width: "100%",
                padding: "12px",
                border: "2px solid #e1e5e9",
                borderRadius: "8px",
                fontSize: "16px",
                fontFamily: "monospace",
                letterSpacing: "1px",
                textTransform: "uppercase",
                outline: "none",
                transition: "border-color 0.2s",
              }}
              onFocus={(e) => e.target.style.borderColor = "#667eea"}
              onBlur={(e) => e.target.style.borderColor = "#e1e5e9"}
            />
          </div>

          <div style={{ marginBottom: "20px" }}>
            <label style={{ 
              display: "block", 
              marginBottom: "8px", 
              fontWeight: "500",
              color: "#333"
            }}>
              Email (Optional)
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              style={{
                width: "100%",
                padding: "12px",
                border: "2px solid #e1e5e9",
                borderRadius: "8px",
                fontSize: "16px",
                outline: "none",
                transition: "border-color 0.2s",
              }}
              onFocus={(e) => e.target.style.borderColor = "#667eea"}
              onBlur={(e) => e.target.style.borderColor = "#e1e5e9"}
            />
          </div>

          <div style={{ 
            display: "grid", 
            gridTemplateColumns: "1fr 1fr", 
            gap: "15px",
            marginBottom: "30px"
          }}>
            <div>
              <label style={{ 
                display: "block", 
                marginBottom: "8px", 
                fontWeight: "500",
                color: "#333"
              }}>
                First Name
              </label>
              <input
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                placeholder="First name"
                style={{
                  width: "100%",
                  padding: "12px",
                  border: "2px solid #e1e5e9",
                  borderRadius: "8px",
                  fontSize: "16px",
                  outline: "none",
                  transition: "border-color 0.2s",
                }}
                onFocus={(e) => e.target.style.borderColor = "#667eea"}
                onBlur={(e) => e.target.style.borderColor = "#e1e5e9"}
              />
            </div>
            <div>
              <label style={{ 
                display: "block", 
                marginBottom: "8px", 
                fontWeight: "500",
                color: "#333"
              }}>
                Last Name
              </label>
              <input
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                placeholder="Last name"
                style={{
                  width: "100%",
                  padding: "12px",
                  border: "2px solid #e1e5e9",
                  borderRadius: "8px",
                  fontSize: "16px",
                  outline: "none",
                  transition: "border-color 0.2s",
                }}
                onFocus={(e) => e.target.style.borderColor = "#667eea"}
                onBlur={(e) => e.target.style.borderColor = "#e1e5e9"}
              />
            </div>
          </div>

          <button
            type="submit"
            disabled={loading || !code.trim()}
            style={{
              width: "100%",
              padding: "15px",
              background: loading || !code.trim() ? "#ccc" : "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              color: "white",
              border: "none",
              borderRadius: "8px",
              fontSize: "16px",
              fontWeight: "600",
              cursor: loading || !code.trim() ? "not-allowed" : "pointer",
              transition: "all 0.2s",
              transform: loading ? "none" : "translateY(0)",
            }}
            onMouseEnter={(e) => {
              if (!loading && code.trim()) {
                e.currentTarget.style.transform = "translateY(-2px)";
                e.currentTarget.style.boxShadow = "0 10px 20px rgba(0,0,0,0.2)";
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = "translateY(0)";
              e.currentTarget.style.boxShadow = "none";
            }}
          >
            {loading ? "Validating..." : "Validate Code"}
          </button>
        </form>

        {result && (
          <div style={{
            marginTop: "20px",
            padding: "15px",
            borderRadius: "8px",
            background: result.success ? "#d4edda" : "#f8d7da",
            border: `1px solid ${result.success ? "#c3e6cb" : "#f5c6cb"}`,
            color: result.success ? "#155724" : "#721c24"
          }}>
            <strong>{result.success ? "Success!" : "Error:"}</strong>
            <p style={{ margin: "5px 0 0 0" }}>{result.message}</p>
            {result.success && result.data && (
              <div style={{ marginTop: "10px", fontSize: "14px" }}>
                <p>Remaining uses: {result.data.remainingUses}/{result.data.maxUses}</p>
                {result.data.expiresAt && (
                  <p>Expires: {new Date(result.data.expiresAt).toLocaleDateString()}</p>
                )}
              </div>
            )}
          </div>
        )}

        <div style={{ 
          marginTop: "30px", 
          textAlign: "center",
          fontSize: "14px",
          color: "#666"
        }}>
          <p>
            Don't have an invite code? Contact the store owner for access.
          </p>
        </div>
      </div>
    </div>
  );
}
