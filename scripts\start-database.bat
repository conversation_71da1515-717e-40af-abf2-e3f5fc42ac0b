@echo off
echo.
echo ========================================
echo  Supersleek Invite Code - Database Setup
echo ========================================
echo.

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed or not in PATH
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop/
    echo.
    pause
    exit /b 1
)

echo ✅ Docker is installed
echo.

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running
    echo Please start Docker Desktop and try again
    echo.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

echo 🚀 Starting PostgreSQL database...
docker-compose up -d

if %errorlevel% neq 0 (
    echo ❌ Failed to start database containers
    echo Please check the error messages above
    echo.
    pause
    exit /b 1
)

echo.
echo ⏳ Waiting for database to be ready...
timeout /t 10 /nobreak >nul

REM Test database connection
echo 🔍 Testing database connection...
docker-compose exec -T postgres psql -U invite_app -d supertested_invite_codes -c "SELECT 'Database is ready!' as status;" >nul 2>&1

if %errorlevel% neq 0 (
    echo ⚠️  Database is starting up, please wait a moment...
    timeout /t 10 /nobreak >nul
    docker-compose exec -T postgres psql -U invite_app -d supertested_invite_codes -c "SELECT 'Database is ready!' as status;" >nul 2>&1
)

if %errorlevel% equ 0 (
    echo ✅ Database connection successful!
) else (
    echo ⚠️  Database might still be starting up
)

echo.
echo 📊 Container status:
docker-compose ps

echo.
echo ========================================
echo  Database Setup Complete!
echo ========================================
echo.
echo 🔗 Services available:
echo   • PostgreSQL: localhost:5432
echo   • pgAdmin: http://localhost:8080
echo.
echo 📝 Database credentials:
echo   • Database: supertested_invite_codes
echo   • Username: invite_app
echo   • Password: dev_password_2024
echo.
echo 🌐 pgAdmin login:
echo   • Email: <EMAIL>
echo   • Password: admin123
echo.
echo 🚀 Next steps:
echo   1. Run: npx prisma migrate dev --name init
echo   2. Run: npx prisma db seed (optional)
echo   3. Run: npm run dev
echo.
echo Press any key to continue...
pause >nul
