import crypto from "crypto";

export interface CustomerData {
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  accepts_marketing?: boolean;
  marketing_opt_in_level?: "single_opt_in" | "confirmed_opt_in" | "unknown";
  email_marketing_consent?: {
    state: "subscribed" | "unsubscribed" | "not_subscribed";
    opt_in_level: "single_opt_in" | "confirmed_opt_in" | "unknown";
    consent_updated_at?: string;
  };
  sms_marketing_consent?: {
    state: "subscribed" | "unsubscribed" | "not_subscribed";
    opt_in_level: "single_opt_in" | "confirmed_opt_in" | "unknown";
    consent_updated_at?: string;
  };
  // Custom attributes for invite code tracking
  note?: string;
  tags?: string[];
  metafields?: Array<{
    namespace: string;
    key: string;
    value: string;
    type?: string;
  }>;
  // Return URL after login
  return_to?: string;
}

export class CustomerAuthManager {
  private shopDomain: string;
  private accessToken: string;
  private storefrontAccessToken: string;

  constructor(shopDomain: string, accessToken: string, storefrontAccessToken: string) {
    if (!shopDomain || !accessToken || !storefrontAccessToken) {
      throw new Error("Shop domain, access token, and storefront access token are required");
    }

    this.shopDomain = shopDomain.includes(".") ? shopDomain : `${shopDomain}.myshopify.com`;
    this.accessToken = accessToken;
    this.storefrontAccessToken = storefrontAccessToken;
  }

  /**
   * Create a customer account using Admin API
   */
  async createCustomer(customerData: CustomerData): Promise<{ customer: any; success: boolean; error?: string }> {
    try {
      const customer = {
        email: customerData.email,
        first_name: customerData.first_name || "Invited",
        last_name: customerData.last_name || "Customer",
        phone: customerData.phone,
        accepts_marketing: customerData.accepts_marketing || false,
        marketing_opt_in_level: customerData.marketing_opt_in_level || "unknown",
        email_marketing_consent: customerData.email_marketing_consent,
        sms_marketing_consent: customerData.sms_marketing_consent,
        note: customerData.note,
        tags: customerData.tags?.join(", "),
        metafields: customerData.metafields,
        send_email_invite: false, // Don't send Shopify's default email
        password_confirmation: this.generateSecurePassword(), // Generate secure password
      };

      const response = await fetch(`https://${this.shopDomain}/admin/api/2025-01/customers.json`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": this.accessToken,
        },
        body: JSON.stringify({ customer }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          customer: null,
          success: false,
          error: errorData.errors || "Failed to create customer"
        };
      }

      const data = await response.json();
      return { customer: data.customer, success: true };
    } catch (error) {
      return {
        customer: null,
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  /**
   * Generate a customer access token using Storefront API
   */
  async generateCustomerAccessToken(email: string, password: string): Promise<{ accessToken: string | null; success: boolean; error?: string }> {
    try {
      const mutation = `
        mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
          customerAccessTokenCreate(input: $input) {
            customerAccessToken {
              accessToken
              expiresAt
            }
            customerUserErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          email,
          password,
        },
      };

      const response = await fetch(`https://${this.shopDomain}/api/2025-01/graphql.json`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Storefront-Access-Token": this.storefrontAccessToken,
        },
        body: JSON.stringify({ query: mutation, variables }),
      });

      if (!response.ok) {
        return {
          accessToken: null,
          success: false,
          error: "Failed to generate access token"
        };
      }

      const data = await response.json();

      if (data.data?.customerAccessTokenCreate?.customerUserErrors?.length > 0) {
        return {
          accessToken: null,
          success: false,
          error: data.data.customerAccessTokenCreate.customerUserErrors[0].message
        };
      }

      const accessToken = data.data?.customerAccessTokenCreate?.customerAccessToken?.accessToken;

      if (!accessToken) {
        return {
          accessToken: null,
          success: false,
          error: "No access token returned"
        };
      }

      return { accessToken, success: true };
    } catch (error) {
      return {
        accessToken: null,
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  /**
   * Generate a secure random password
   */
  private generateSecurePassword(): string {
    return crypto.randomBytes(16).toString("hex") + "!A1";
  }

  /**
   * Generate customer login URL for Customer Account API
   */
  generateLoginUrl(returnTo?: string): string {
    const baseUrl = `https://${this.shopDomain}/account/login`;
    if (returnTo) {
      return `${baseUrl}?return_url=${encodeURIComponent(returnTo)}`;
    }
    return baseUrl;
  }
}

/**
 * Create a Customer Auth Manager instance
 */
export function createCustomerAuthManager(): CustomerAuthManager {
  const shopDomain = process.env.DEV_SHOP_DOMAIN || "supersleek-staging.myshopify.com";
  const accessToken = process.env.SHOPIFY_ACCESS_TOKEN;
  const storefrontAccessToken = process.env.SHOPIFY_STOREFRONT_ACCESS_TOKEN;

  if (!accessToken) {
    throw new Error("SHOPIFY_ACCESS_TOKEN environment variable is required");
  }

  if (!storefrontAccessToken) {
    throw new Error("SHOPIFY_STOREFRONT_ACCESS_TOKEN environment variable is required");
  }

  return new CustomerAuthManager(shopDomain, accessToken, storefrontAccessToken);
}

/**
 * Create customer account and generate login session
 * Alternative to Multipass for Shopify Basic plans
 */
export async function createCustomerWithInviteCode(
  inviteCode: string,
  customerData: Partial<CustomerData> = {}
): Promise<{
  success: boolean;
  customer?: any;
  accessToken?: string;
  loginUrl?: string;
  error?: string
}> {
  try {
    const authManager = createCustomerAuthManager();

    // Prepare customer data with invite code tracking
    const data: CustomerData = {
      email: customerData.email || `invite-${inviteCode}-${Date.now()}@temp.supersleek.com`,
      first_name: customerData.first_name || "Invited",
      last_name: customerData.last_name || "Customer",
      accepts_marketing: customerData.accepts_marketing ?? false,
      marketing_opt_in_level: customerData.marketing_opt_in_level || "unknown",
      note: `Customer created via invite code: ${inviteCode}`,
      tags: [`invite-code:${inviteCode}`, "supersleek-user", ...(customerData.tags || [])],
      metafields: [
        {
          namespace: "supersleek",
          key: "invite_code",
          value: inviteCode,
          type: "single_line_text_field"
        },
        {
          namespace: "supersleek",
          key: "signup_method",
          value: "invite_code",
          type: "single_line_text_field"
        },
        {
          namespace: "supersleek",
          key: "signup_date",
          value: new Date().toISOString(),
          type: "date_time"
        },
        ...(customerData.metafields || [])
      ],
      return_to: customerData.return_to || "/",
      ...customerData,
    };

    // Create customer account
    const customerResult = await authManager.createCustomer(data);

    if (!customerResult.success) {
      return {
        success: false,
        error: customerResult.error || "Failed to create customer account"
      };
    }

    // Generate login URL (Customer Account API approach)
    const loginUrl = authManager.generateLoginUrl(data.return_to);

    return {
      success: true,
      customer: customerResult.customer,
      loginUrl,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

/**
 * Generate a temporary login token for immediate authentication
 * This creates a secure token that can be used for one-time login
 */
export function generateTemporaryLoginToken(customerId: string, inviteCode: string): string {
  const data = {
    customerId,
    inviteCode,
    timestamp: Date.now(),
    nonce: crypto.randomBytes(16).toString("hex"),
  };

  const token = Buffer.from(JSON.stringify(data)).toString("base64url");
  return token;
}

/**
 * Verify and decode temporary login token
 */
export function verifyTemporaryLoginToken(token: string): {
  valid: boolean;
  customerId?: string;
  inviteCode?: string;
  error?: string
} {
  try {
    const decoded = JSON.parse(Buffer.from(token, "base64url").toString());

    // Check if token is not older than 10 minutes
    const maxAge = 10 * 60 * 1000; // 10 minutes
    if (Date.now() - decoded.timestamp > maxAge) {
      return { valid: false, error: "Token expired" };
    }

    return {
      valid: true,
      customerId: decoded.customerId,
      inviteCode: decoded.inviteCode,
    };
  } catch (error) {
    return { valid: false, error: "Invalid token format" };
  }
}
