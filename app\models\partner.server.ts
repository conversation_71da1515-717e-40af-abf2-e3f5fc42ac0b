import { PrismaClient } from "@prisma/client";
import type { Partner } from "@prisma/client";

const db = new PrismaClient();

export interface CreatePartnerData {
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  notes?: string;
  shopId: string;
}

export interface UpdatePartnerData {
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  notes?: string;
  isActive?: boolean;
}

export interface PartnerFilters {
  shopId: string;
  isActive?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

// Create a new partner
export async function createPartner(data: CreatePartnerData): Promise<Partner> {
  console.log("🆕 Creating partner:", data);

  try {
    const partner = await db.partner.create({
      data: {
        name: data.name,
        email: data.email,
        phone: data.phone,
        company: data.company,
        notes: data.notes,
        shopId: data.shopId,
      },
    });

    console.log("✅ Partner created successfully:", partner.id);
    return partner;
  } catch (error) {
    console.error("❌ Error creating partner:", error);
    throw new Error("Failed to create partner");
  }
}

// Get all partners for a shop
export async function getPartners(filters: PartnerFilters): Promise<{
  partners: Partner[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}> {
  const page = filters.page || 1;
  const limit = filters.limit || 20;
  const offset = (page - 1) * limit;

  console.log("📋 Getting partners with filters:", filters);

  try {
    const where: any = {
      shopId: filters.shopId,
    };

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } },
        { company: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    const [partners, total] = await Promise.all([
      db.partner.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      db.partner.count({ where }),
    ]);

    const pages = Math.ceil(total / limit);

    console.log(`✅ Found ${partners.length} partners (${total} total)`);

    return {
      partners,
      pagination: {
        page,
        limit,
        total,
        pages,
      },
    };
  } catch (error) {
    console.error("❌ Error getting partners:", error);
    throw new Error("Failed to get partners");
  }
}

// Get a single partner by ID
export async function getPartnerById(id: number, shopId: string): Promise<Partner | null> {
  console.log(`🔍 Getting partner ${id} for shop ${shopId}`);

  try {
    const partner = await db.partner.findFirst({
      where: {
        id,
        shopId,
      },
    });

    if (partner) {
      console.log("✅ Partner found:", partner.name);
    } else {
      console.log("❌ Partner not found");
    }

    return partner;
  } catch (error) {
    console.error("❌ Error getting partner:", error);
    throw new Error("Failed to get partner");
  }
}

// Update a partner
export async function updatePartner(
  id: number,
  shopId: string,
  data: UpdatePartnerData
): Promise<Partner> {
  console.log(`🔄 Updating partner ${id}:`, data);

  try {
    const partner = await db.partner.update({
      where: {
        id,
        shopId,
      },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });

    console.log("✅ Partner updated successfully");
    return partner;
  } catch (error) {
    console.error("❌ Error updating partner:", error);
    throw new Error("Failed to update partner");
  }
}

// Delete a partner
export async function deletePartner(id: number, shopId: string): Promise<void> {
  console.log(`🗑️ Deleting partner ${id}`);

  try {
    // First, check if partner has any invite codes
    const inviteCodesCount = await db.inviteCode.count({
      where: { partnerId: id },
    });

    if (inviteCodesCount > 0) {
      // Set partner to null for all associated invite codes instead of deleting
      await db.inviteCode.updateMany({
        where: { partnerId: id },
        data: { partnerId: null },
      });
      console.log(`🔗 Unlinked ${inviteCodesCount} invite codes from partner`);
    }

    await db.partner.delete({
      where: {
        id,
        shopId,
      },
    });

    console.log("✅ Partner deleted successfully");
  } catch (error) {
    console.error("❌ Error deleting partner:", error);
    throw new Error("Failed to delete partner");
  }
}

// Get partners for dropdown (simplified)
export async function getPartnersForDropdown(shopId: string): Promise<Array<{ id: number; name: string }>> {
  console.log("📋 Getting partners for dropdown");

  try {
    const partners = await db.partner.findMany({
      where: {
        shopId,
        isActive: true,
      },
      select: {
        id: true,
        name: true,
      },
      orderBy: { name: 'asc' },
    });

    console.log(`✅ Found ${partners.length} active partners for dropdown`);
    return partners;
  } catch (error) {
    console.error("❌ Error getting partners for dropdown:", error);
    throw new Error("Failed to get partners for dropdown");
  }
}
