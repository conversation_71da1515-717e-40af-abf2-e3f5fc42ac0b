/* Accessibility Enhancements */

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip Links for Keyboard Navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--color-text-inverse);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-small);
  z-index: 1000;
  font-weight: 600;
}

.skip-link:focus {
  top: 6px;
}

/* Enhanced Focus Indicators */
.focus-ring:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-small);
}

/* Button Focus States */
button:focus,
.button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Input Focus States */
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 1px;
  border-color: var(--color-primary);
}

/* High Contrast Mode Adjustments */
@media (prefers-contrast: high) {
  .button,
  button {
    border: 2px solid currentColor;
  }
  
  .card,
  .panel {
    border: 1px solid currentColor;
  }
  
  .input,
  input,
  textarea,
  select {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-in,
  .animate-scale-in {
    animation: none !important;
  }
  
  .transition-all,
  .transition-colors,
  .transition-transform {
    transition: none !important;
  }
}

/* Color Blind Friendly Patterns */
.status-success::before {
  content: "✓ ";
  font-weight: bold;
}

.status-error::before {
  content: "✗ ";
  font-weight: bold;
}

.status-warning::before {
  content: "⚠ ";
  font-weight: bold;
}

.status-info::before {
  content: "ℹ ";
  font-weight: bold;
}

/* Keyboard Navigation Helpers */
.keyboard-nav {
  display: none;
}

body.using-keyboard .keyboard-nav {
  display: block;
}

/* Focus Trap for Modals */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: "";
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Improved Link Accessibility */
a:not([class]) {
  color: var(--color-primary);
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
}

a:not([class]):hover {
  text-decoration-thickness: 3px;
}

a:not([class]):focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: 2px;
}

/* Form Label Improvements */
label {
  font-weight: 600;
  margin-bottom: var(--space-xs);
  display: block;
}

.required::after {
  content: " *";
  color: var(--color-error);
  font-weight: bold;
}

/* Error Message Styling */
.error-message {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--space-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.error-message::before {
  content: "⚠";
  font-weight: bold;
}

/* Success Message Styling */
.success-message {
  color: var(--color-success);
  font-size: var(--font-size-sm);
  margin-top: var(--space-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.success-message::before {
  content: "✓";
  font-weight: bold;
}

/* Loading States */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-border-light);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Text Sizing */
@media (max-width: 768px) {
  body {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
  }
  
  .text-sm {
    font-size: var(--font-size-base);
  }
  
  .text-xs {
    font-size: var(--font-size-sm);
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  a {
    text-decoration: underline !important;
  }
  
  .shadow-light,
  .shadow-medium,
  .shadow-heavy {
    box-shadow: none !important;
  }
}
