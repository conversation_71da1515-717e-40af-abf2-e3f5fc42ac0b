import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useActionData, useLoaderData, useNavigation, useSubmit } from "@remix-run/react";
import { useState } from "react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  FormLayout,
  TextField,
  Button,
  InlineStack,
  Banner,
  ResourceList,
  ResourceItem,
  Icon,
  Select,
  Tabs,
  Divider,
} from "@shopify/polaris";
import { DeleteIcon, PlusIcon, SettingsIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import { ThemeToggle } from "../components/UI/ThemeToggle";
import { getMembershipTags, createMembershipTag, deleteMembershipTag } from "../models/membership-tag.server";
import {
  getAppSettings,
  updateAppSettings,
  getDateFormatOptions,
  getTimeFormatOptions,
  getTimezoneOptions
} from "../models/app-settings.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    const [membershipTags, appSettings] = await Promise.all([
      getMembershipTags(shop),
      getAppSettings(shop),
    ]);

    return json({
      membershipTags,
      appSettings,
      shop,
      dateFormatOptions: getDateFormatOptions(),
      timeFormatOptions: getTimeFormatOptions(),
      timezoneOptions: getTimezoneOptions(),
    });
  } catch (error) {
    console.error("Error loading settings:", error);
    return json({
      membershipTags: [],
      appSettings: null,
      shop,
      dateFormatOptions: getDateFormatOptions(),
      timeFormatOptions: getTimeFormatOptions(),
      timezoneOptions: getTimezoneOptions(),
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  const formData = await request.formData();
  const action = formData.get("action");

  try {
    // Handle membership tag actions
    if (action === "create") {
      const name = formData.get("name") as string;
      const customerTag = formData.get("customerTag") as string;
      const orderTag = formData.get("orderTag") as string;

      if (!name?.trim()) {
        return json({ error: "Membership name is required" }, { status: 400 });
      }

      if (!customerTag?.trim()) {
        return json({ error: "Customer tag is required" }, { status: 400 });
      }

      await createMembershipTag(shop, {
        name: name.trim(),
        customerTag: customerTag.trim(),
        orderTag: orderTag?.trim() || null,
      });

      return json({ success: true, message: "Membership tag created successfully" });
    }

    // Handle app settings update
    if (action === "updateSettings") {
      const dateFormat = formData.get("dateFormat") as string;
      const timeFormat = formData.get("timeFormat") as string;
      const timezone = formData.get("timezone") as string;
      const defaultExpiryDays = formData.get("defaultExpiryDays") as string;
      const enableEmailNotifications = formData.get("enableEmailNotifications") === "true";
      const maxCodesPerBatch = formData.get("maxCodesPerBatch") as string;

      const updateData: any = {};

      if (dateFormat) updateData.dateFormat = dateFormat;
      if (timeFormat) updateData.timeFormat = timeFormat;
      if (timezone) updateData.timezone = timezone;
      if (defaultExpiryDays) {
        const days = parseInt(defaultExpiryDays);
        updateData.defaultExpiryDays = isNaN(days) ? null : days;
      }
      updateData.enableEmailNotifications = enableEmailNotifications;
      if (maxCodesPerBatch) {
        const maxCodes = parseInt(maxCodesPerBatch);
        updateData.maxCodesPerBatch = isNaN(maxCodes) ? 100 : Math.max(1, Math.min(1000, maxCodes));
      }

      await updateAppSettings(shop, updateData);

      return json({ success: true, message: "Settings updated successfully" });
    }

    if (action === "delete") {
      const id = formData.get("id") as string;
      if (!id) {
        return json({ error: "Membership tag ID is required" }, { status: 400 });
      }

      await deleteMembershipTag(shop, parseInt(id));
      return json({ success: true, message: "Membership tag deleted successfully" });
    }

    return json({ error: "Invalid action" }, { status: 400 });
  } catch (error) {
    console.error("Error in settings action:", error);
    return json(
      { error: "Failed to process request. Please try again." },
      { status: 500 }
    );
  }
};

export default function SettingsPage() {
  const {
    membershipTags,
    appSettings,
    dateFormatOptions,
    timeFormatOptions,
    timezoneOptions
  } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const submit = useSubmit();

  const [selectedTab, setSelectedTab] = useState(0);
  const [formData, setFormData] = useState({
    name: "",
    customerTag: "",
    orderTag: "",
  });

  const [settingsData, setSettingsData] = useState({
    dateFormat: appSettings?.dateFormat || "YYYY-MM-DD",
    timeFormat: appSettings?.timeFormat || "24h",
    timezone: appSettings?.timezone || "UTC",
    defaultExpiryDays: appSettings?.defaultExpiryDays?.toString() || "",
    enableEmailNotifications: appSettings?.enableEmailNotifications ?? true,
    maxCodesPerBatch: appSettings?.maxCodesPerBatch?.toString() || "100",
  });

  const isSubmitting = navigation.state === "submitting";

  const tabs = [
    {
      id: "general",
      content: "General Settings",
      accessibilityLabel: "General application settings",
      panelID: "general-settings-panel",
    },
    {
      id: "membership",
      content: "Membership Tags",
      accessibilityLabel: "Membership tag management",
      panelID: "membership-tags-panel",
    },
  ];

  const handleFieldChange = (field: string) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSettingsChange = (field: string) => (value: string | boolean) => {
    setSettingsData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const submitData = new FormData();
    submitData.append("action", "create");
    submitData.append("name", formData.name);
    submitData.append("customerTag", formData.customerTag);
    submitData.append("orderTag", formData.orderTag);

    submit(submitData, { method: "POST" });

    // Reset form on successful submission
    if (actionData?.success) {
      setFormData({ name: "", customerTag: "", orderTag: "" });
    }
  };

  const handleSettingsSubmit = () => {
    const submitData = new FormData();
    submitData.append("action", "updateSettings");
    submitData.append("dateFormat", settingsData.dateFormat);
    submitData.append("timeFormat", settingsData.timeFormat);
    submitData.append("timezone", settingsData.timezone);
    submitData.append("defaultExpiryDays", settingsData.defaultExpiryDays);
    submitData.append("enableEmailNotifications", settingsData.enableEmailNotifications.toString());
    submitData.append("maxCodesPerBatch", settingsData.maxCodesPerBatch);

    submit(submitData, { method: "POST" });
  };

  const handleDelete = (id: number) => {
    const submitData = new FormData();
    submitData.append("action", "delete");
    submitData.append("id", id.toString());
    submit(submitData, { method: "POST" });
  };

  return (
    <Page
      title="Settings"
      subtitle="Manage membership tags and application settings"
      backAction={{
        content: "Back to Dashboard",
        url: "/app",
      }}
    >
      <Box paddingBlockEnd="400">
        <InlineStack align="end">
          <ThemeToggle />
        </InlineStack>
      </Box>

      {actionData?.error && (
        <Box paddingBlockEnd="400">
          <Banner tone="critical">
            <p>{actionData.error}</p>
          </Banner>
        </Box>
      )}

      {actionData?.success && (
        <Box paddingBlockEnd="400">
          <Banner tone="success">
            <p>{actionData.message}</p>
          </Banner>
        </Box>
      )}

      <Card>
        <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab}>
          <Box padding="400">
            {selectedTab === 0 && (
              <div id="general-settings-panel">
                <Text variant="headingMd" as="h2">
                  <InlineStack gap="200" align="center">
                    <Icon source={SettingsIcon} />
                    General Settings
                  </InlineStack>
                </Text>
                <Text variant="bodyMd" as="p" tone="subdued">
                  Configure date formats, time zones, and default behaviors for your invite codes.
                </Text>

                <Box paddingBlockStart="400">
                  <FormLayout>
                    <FormLayout.Group>
                      <Select
                        label="Date Format"
                        options={dateFormatOptions}
                        value={settingsData.dateFormat}
                        onChange={handleSettingsChange("dateFormat")}
                        helpText="Choose how dates are displayed throughout the app"
                      />
                      <Select
                        label="Time Format"
                        options={timeFormatOptions}
                        value={settingsData.timeFormat}
                        onChange={handleSettingsChange("timeFormat")}
                        helpText="Choose between 12-hour or 24-hour time format"
                      />
                    </FormLayout.Group>

                    <Select
                      label="Timezone"
                      options={timezoneOptions}
                      value={settingsData.timezone}
                      onChange={handleSettingsChange("timezone")}
                      helpText="Default timezone for date and time displays"
                    />

                    <FormLayout.Group>
                      <TextField
                        label="Default Expiry Days"
                        type="number"
                        value={settingsData.defaultExpiryDays}
                        onChange={handleSettingsChange("defaultExpiryDays")}
                        helpText="Default number of days until invite codes expire (leave empty for no default)"
                        min="1"
                        max="365"
                      />
                      <TextField
                        label="Max Codes Per Batch"
                        type="number"
                        value={settingsData.maxCodesPerBatch}
                        onChange={handleSettingsChange("maxCodesPerBatch")}
                        helpText="Maximum number of codes that can be generated in one batch"
                        min="1"
                        max="1000"
                      />
                    </FormLayout.Group>

                    <Box paddingBlockStart="400">
                      <Button
                        variant="primary"
                        onClick={handleSettingsSubmit}
                        loading={isSubmitting}
                        disabled={isSubmitting}
                      >
                        Save Settings
                      </Button>
                    </Box>
                  </FormLayout>
                </Box>
              </div>
            )}

            {selectedTab === 1 && (
              <div id="membership-tags-panel">
                <Text variant="headingMd" as="h2">
                  <InlineStack gap="200" align="center">
                    <Icon source={PlusIcon} />
                    Membership Tags
                  </InlineStack>
                </Text>
                <Text variant="bodyMd" as="p" tone="subdued">
                  Create and manage membership tags that will be applied to customers when they use invite codes.
                </Text>

                <Box paddingBlockStart="400">
                  <Layout>
                    <Layout.Section variant="oneHalf">
                      <Card>
                        <Box padding="400">
                          <Text variant="headingMd" as="h3">
                            Add New Membership Tag
                          </Text>

                          <Box paddingBlockStart="400">
                            <form onSubmit={handleSubmit}>
                              <FormLayout>
                                <TextField
                                  label="Membership Name"
                                  value={formData.name}
                                  onChange={handleFieldChange("name")}
                                  placeholder="e.g., Gold Membership"
                                  helpText="Display name for this membership level"
                                  autoComplete="off"
                                  disabled={isSubmitting}
                                  required
                                />

                                <TextField
                                  label="Customer Tag Name"
                                  value={formData.customerTag}
                                  onChange={handleFieldChange("customerTag")}
                                  placeholder="e.g., gold-member"
                                  helpText="Tag that will be added to customers (lowercase, no spaces)"
                                  autoComplete="off"
                                  disabled={isSubmitting}
                                  required
                                />

                                <TextField
                                  label="Order Tag Name (Optional)"
                                  value={formData.orderTag}
                                  onChange={handleFieldChange("orderTag")}
                                  placeholder="e.g., gold-member-order"
                                  helpText="Tag that will be added to orders (optional)"
                                  autoComplete="off"
                                  disabled={isSubmitting}
                                />

                                <InlineStack align="end">
                                  <Button
                                    variant="primary"
                                    submit
                                    loading={isSubmitting}
                                    icon={PlusIcon}
                                  >
                                    Add Membership Tag
                                  </Button>
                                </InlineStack>
                              </FormLayout>
                            </form>
                          </Box>
                        </Box>
                      </Card>
                    </Layout.Section>

                    <Layout.Section variant="oneHalf">
                      <Card>
                        <Box padding="400">
                          <Text variant="headingMd" as="h3">
                            Existing Membership Tags
                          </Text>
                          <Text variant="bodyMd" as="p" tone="subdued">
                            Manage your current membership tag configurations.
                          </Text>

                          <Box paddingBlockStart="400">
                            {membershipTags.length === 0 ? (
                              <Box padding="400" background="bg-surface-secondary">
                                <Text variant="bodyMd" as="p" alignment="center" tone="subdued">
                                  No membership tags configured yet. Add your first membership tag to get started.
                                </Text>
                              </Box>
                            ) : (
                              <ResourceList
                                resourceName={{ singular: "membership tag", plural: "membership tags" }}
                                items={membershipTags}
                                renderItem={(tag) => (
                                  <ResourceItem
                                    id={tag.id.toString()}
                                    accessibilityLabel={`Membership tag ${tag.name}`}
                                  >
                                    <InlineStack align="space-between" blockAlign="center">
                                      <div>
                                        <Text variant="bodyMd" fontWeight="medium" as="h3">
                                          {tag.name}
                                        </Text>
                                        <Text variant="bodySm" tone="subdued" as="p">
                                          Customer: {tag.customerTag}
                                          {tag.orderTag && ` • Order: ${tag.orderTag}`}
                                        </Text>
                                      </div>
                                      <Button
                                        variant="tertiary"
                                        tone="critical"
                                        icon={DeleteIcon}
                                        onClick={() => handleDelete(tag.id)}
                                        accessibilityLabel={`Delete ${tag.name}`}
                                        disabled={isSubmitting}
                                      />
                                    </InlineStack>
                                  </ResourceItem>
                                )}
                              />
                            )}
                          </Box>
                        </Box>
                      </Card>
                    </Layout.Section>
                  </Layout>
                </Box>
              </div>
            )}
          </Box>
        </Tabs>
      </Card>
    </Page>
  );
}
