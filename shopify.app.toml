# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "b44e3f0f0eae23878088a793cfd38091"
name = "Supertested Invite Code"
handle = "supertested-invite-code"
application_url = "https://validity-doctrine-expand-defensive.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = ["https://validity-doctrine-expand-defensive.trycloudflare.com/auth/callback", "https://validity-doctrine-expand-defensive.trycloudflare.com/auth/shopify/callback", "https://validity-doctrine-expand-defensive.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
