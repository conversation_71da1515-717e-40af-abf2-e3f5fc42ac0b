# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "b44e3f0f0eae23878088a793cfd38091"
name = "Supertested Invite Code"
handle = "supertested-invite-code"
application_url = "https://moscow-scoop-pk-wheat.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true

[webhooks]
api_version = "2025-01"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "orders/paid" ]
  uri = "/webhooks/orders/paid"

  [[webhooks.subscriptions]]
  topics = [ "checkouts/create" ]
  uri = "/webhooks/checkouts/create"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = ["https://moscow-scoop-pk-wheat.trycloudflare.com/auth/callback", "https://moscow-scoop-pk-wheat.trycloudflare.com/auth/shopify/callback", "https://moscow-scoop-pk-wheat.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
