# Supersleek Invite Code - Database Setup Script (PowerShell)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " Supersleek Invite Code - Database Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Docker is installed
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker is installed: $dockerVersion" -ForegroundColor Green
    } else {
        throw "Docker not found"
    }
} catch {
    Write-Host "❌ Docker is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Docker is running
try {
    docker info 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker is running" -ForegroundColor Green
    } else {
        throw "Docker not running"
    }
} catch {
    Write-Host "❌ Docker is not running" -ForegroundColor Red
    Write-Host "Please start Docker Desktop and try again" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "🚀 Starting PostgreSQL database..." -ForegroundColor Blue

# Start Docker containers
try {
    docker-compose up -d
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database containers started successfully" -ForegroundColor Green
    } else {
        throw "Failed to start containers"
    }
} catch {
    Write-Host "❌ Failed to start database containers" -ForegroundColor Red
    Write-Host "Please check the error messages above" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "⏳ Waiting for database to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Test database connection
Write-Host "🔍 Testing database connection..." -ForegroundColor Blue

$maxRetries = 3
$retryCount = 0
$connected = $false

while ($retryCount -lt $maxRetries -and -not $connected) {
    try {
        $result = docker-compose exec -T postgres psql -U invite_app -d supersleek_invite_codes -c "SELECT 'Database is ready!' as status;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            $connected = $true
            Write-Host "✅ Database connection successful!" -ForegroundColor Green
        } else {
            throw "Connection failed"
        }
    } catch {
        $retryCount++
        if ($retryCount -lt $maxRetries) {
            Write-Host "⚠️  Database is starting up, retrying in 10 seconds... ($retryCount/$maxRetries)" -ForegroundColor Yellow
            Start-Sleep -Seconds 10
        }
    }
}

if (-not $connected) {
    Write-Host "⚠️  Database might still be starting up. You can check manually with:" -ForegroundColor Yellow
    Write-Host "   docker-compose logs postgres" -ForegroundColor Gray
}

Write-Host ""
Write-Host "📊 Container status:" -ForegroundColor Blue
docker-compose ps

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " Database Setup Complete!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔗 Services available:" -ForegroundColor Green
Write-Host "  • PostgreSQL: localhost:5432" -ForegroundColor White
Write-Host "  • pgAdmin: http://localhost:8080" -ForegroundColor White
Write-Host ""

Write-Host "📝 Database credentials:" -ForegroundColor Green
Write-Host "  • Database: supertested_invite_codes" -ForegroundColor White
Write-Host "  • Username: invite_app" -ForegroundColor White
Write-Host "  • Password: dev_password_2024" -ForegroundColor White
Write-Host ""

Write-Host "🌐 pgAdmin login:" -ForegroundColor Green
Write-Host "  • Email: <EMAIL>" -ForegroundColor White
Write-Host "  • Password: admin123" -ForegroundColor White
Write-Host ""

Write-Host "🚀 Next steps:" -ForegroundColor Green
Write-Host "  1. Run: npx prisma migrate dev --name init" -ForegroundColor White
Write-Host "  2. Run: npx prisma db seed (optional)" -ForegroundColor White
Write-Host "  3. Run: npm run dev" -ForegroundColor White
Write-Host ""

# Ask if user wants to run migrations automatically
$runMigrations = Read-Host "Would you like to run database migrations now? (y/n)"
if ($runMigrations -eq "y" -or $runMigrations -eq "Y") {
    Write-Host ""
    Write-Host "🔄 Running database migrations..." -ForegroundColor Blue
    
    try {
        Write-Host "Generating Prisma client..." -ForegroundColor Yellow
        npx prisma generate
        
        Write-Host "Running migrations..." -ForegroundColor Yellow
        npx prisma migrate dev --name init
        
        $seedDb = Read-Host "Would you like to seed the database with sample data? (y/n)"
        if ($seedDb -eq "y" -or $seedDb -eq "Y") {
            Write-Host "Seeding database..." -ForegroundColor Yellow
            npx prisma db seed
            Write-Host "✅ Database seeded successfully!" -ForegroundColor Green
        }
        
        Write-Host "✅ Database setup completed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Error during migration. Please run manually:" -ForegroundColor Red
        Write-Host "  npx prisma migrate dev --name init" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "🎉 Setup complete! You can now start developing." -ForegroundColor Green
Write-Host ""
Read-Host "Press Enter to exit"
