/**
 * Customer Creation Service
 * Handles creating customers with invite codes and applying appropriate tags
 */

import type { AdminApiContext } from "@shopify/shopify-app-remix/server";
import { getInviteCodeByCode } from "../models/invite-code.server";
import { applyMembershipTagToCustomer, tagCustomerWithInviteCode } from "./customer-tagging.server";

export interface CreateCustomerWithInviteCodeOptions {
  inviteCode: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  acceptsMarketing?: boolean;
  shop: string;
  admin: AdminApiContext;
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
  };
}

export interface CustomerCreationResult {
  success: boolean;
  customer?: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  inviteCodeApplied: boolean;
  membershipApplied: boolean;
  loginUrl?: string;
  error?: string;
}

/**
 * Create a customer account with invite code validation and tag application
 */
export async function createCustomerWithInviteCode({
  inviteCode,
  email,
  firstName,
  lastName,
  acceptsMarketing = false,
  shop,
  admin,
  metadata,
}: CreateCustomerWithInviteCodeOptions): Promise<CustomerCreationResult> {
  console.log(`👤 Creating customer with invite code: ${inviteCode}`);

  try {
    // First, validate the invite code
    const inviteCodeRecord = await getInviteCodeByCode(inviteCode);
    
    if (!inviteCodeRecord) {
      return {
        success: false,
        error: "Invalid invite code",
        inviteCodeApplied: false,
        membershipApplied: false,
      };
    }

    if (!inviteCodeRecord.isActive) {
      return {
        success: false,
        error: "Invite code is inactive",
        inviteCodeApplied: false,
        membershipApplied: false,
      };
    }

    if (inviteCodeRecord.expiresAt && inviteCodeRecord.expiresAt < new Date()) {
      return {
        success: false,
        error: "Invite code has expired",
        inviteCodeApplied: false,
        membershipApplied: false,
      };
    }

    if (inviteCodeRecord.usedCount >= inviteCodeRecord.maxUses) {
      return {
        success: false,
        error: "Invite code has reached maximum uses",
        inviteCodeApplied: false,
        membershipApplied: false,
      };
    }

    // Create the customer account
    const customerInput: any = {
      acceptsMarketing,
    };

    if (email) {
      customerInput.emailAddress = { emailAddress: email };
    }
    if (firstName) {
      customerInput.firstName = firstName;
    }
    if (lastName) {
      customerInput.lastName = lastName;
    }

    console.log(`📝 Creating customer with data:`, customerInput);

    const customerResponse = await admin.graphql(
      `#graphql
        mutation customerCreate($input: CustomerInput!) {
          customerCreate(input: $input) {
            customer {
              id
              firstName
              lastName
              defaultEmailAddress {
                emailAddress
              }
            }
            userErrors {
              field
              message
            }
          }
        }`,
      {
        variables: {
          input: customerInput,
        },
      }
    );

    const customerData = await customerResponse.json();

    if (customerData.data?.customerCreate?.userErrors?.length > 0) {
      const errors = customerData.data.customerCreate.userErrors;
      console.error(`❌ Error creating customer:`, errors);
      return {
        success: false,
        error: `Failed to create customer: ${errors.map((e: any) => e.message).join(', ')}`,
        inviteCodeApplied: false,
        membershipApplied: false,
      };
    }

    const customer = customerData.data?.customerCreate?.customer;
    
    if (!customer) {
      return {
        success: false,
        error: "Failed to create customer account",
        inviteCodeApplied: false,
        membershipApplied: false,
      };
    }

    const customerId = customer.id.replace('gid://shopify/Customer/', '');
    console.log(`✅ Customer created successfully: ${customerId}`);

    // Record the invite code usage
    const { validateAndUseInviteCode } = await import("../models/invite-code.server");
    await validateAndUseInviteCode(inviteCode, {
      customerId,
      ipAddress: metadata?.ipAddress,
      userAgent: metadata?.userAgent,
    });

    let inviteCodeApplied = false;
    let membershipApplied = false;

    // Apply invite code tag
    try {
      const inviteTagResult = await tagCustomerWithInviteCode({
        customerId,
        inviteCode,
        admin,
      });
      inviteCodeApplied = inviteTagResult.success;
      console.log(`🏷️ Invite code tag result: ${inviteTagResult.message}`);
    } catch (error: any) {
      console.error(`❌ Error applying invite code tag:`, error);
    }

    // Apply membership tag if configured
    if (inviteCodeRecord.membershipId) {
      try {
        const membershipResult = await applyMembershipTagToCustomer({
          customerId,
          membershipId: inviteCodeRecord.membershipId,
          shop,
          admin,
        });
        membershipApplied = membershipResult.success;
        console.log(`🏷️ Membership tag result: ${membershipResult.message}`);
      } catch (error: any) {
        console.error(`❌ Error applying membership tag:`, error);
      }
    }

    return {
      success: true,
      customer: {
        id: customerId,
        email: customer.defaultEmailAddress?.emailAddress || email || "",
        firstName: customer.firstName,
        lastName: customer.lastName,
      },
      inviteCodeApplied,
      membershipApplied,
    };

  } catch (error: any) {
    console.error(`❌ Error in customer creation process:`, error);
    return {
      success: false,
      error: `Failed to create customer: ${error.message}`,
      inviteCodeApplied: false,
      membershipApplied: false,
    };
  }
}
