import {
  Card,
  Text,
  Box,
  DataTable,
  Badge,
  InlineStack,
  BlockStack,
  EmptyState,
  Button,
} from "@shopify/polaris";
import type { InviteCodePerformance } from "../../models/analytics.server";

interface TopPerformingCodesProps {
  topPerformingCodes: InviteCodePerformance[];
  onViewCode?: (codeId: number) => void;
}

export function TopPerformingCodes({ topPerformingCodes, onViewCode }: TopPerformingCodesProps) {
  if (topPerformingCodes.length === 0) {
    return (
      <Card>
        <EmptyState
          heading="No invite codes found"
          image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
        >
          <p>Create some invite codes to see performance data here.</p>
        </EmptyState>
      </Card>
    );
  }

  const formatDate = (date: Date | string | null) => {
    if (!date) return "Never";

    // Handle invalid dates
    try {
      const dateObj = date instanceof Date ? date : new Date(date);
      if (isNaN(dateObj.getTime())) return "Invalid Date";

      return new Intl.DateTimeFormat("en-US", {
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      }).format(dateObj);
    } catch (error) {
      console.error("Error formatting date:", error, date);
      return "Invalid Date";
    }
  };

  const getPerformanceBadge = (rate: number, purchases: number) => {
    if (purchases === 0) return <Badge tone="critical">No Sales</Badge>;
    if (rate >= 50) return <Badge tone="success">Excellent</Badge>;
    if (rate >= 25) return <Badge tone="attention">Good</Badge>;
    if (rate >= 10) return <Badge tone="warning">Fair</Badge>;
    return <Badge tone="critical">Poor</Badge>;
  };

  const rows = topPerformingCodes.map((code) => [
    <Box key={`code-${code.id}`}>
      <BlockStack gap="100">
        <InlineStack gap="200" align="start">
          <Text variant="bodyMd" fontWeight="medium">
            {code.code}
          </Text>
          {!code.isActive && <Badge tone="critical">Inactive</Badge>}
        </InlineStack>
        {code.partnerName && (
          <Text variant="bodySm" tone="subdued">
            Partner: {code.partnerName}
          </Text>
        )}
      </BlockStack>
    </Box>,
    
    <Text key={`membership-${code.id}`} variant="bodySm">
      {code.membershipId}
    </Text>,
    
    <Text key={`applications-${code.id}`} variant="bodyMd">
      {code.applications.toLocaleString()}
    </Text>,
    
    <Text key={`purchases-${code.id}`} variant="bodyMd" fontWeight="medium">
      {code.purchases.toLocaleString()}
    </Text>,
    
    <Box key={`performance-${code.id}`}>
      <InlineStack gap="200" align="start">
        <Text variant="bodyMd" fontWeight="medium">
          {code.conversionRate.toFixed(1)}%
        </Text>
        {getPerformanceBadge(code.conversionRate, code.purchases)}
      </InlineStack>
    </Box>,
    
    <Text key={`last-used-${code.id}`} variant="bodySm" tone="subdued">
      {formatDate(code.lastUsed)}
    </Text>,
    
    <Box key={`actions-${code.id}`}>
      {onViewCode && (
        <Button
          variant="plain"
          size="slim"
          onClick={() => onViewCode(code.id)}
        >
          View Details
        </Button>
      )}
    </Box>,
  ]);

  const headings = [
    "Invite Code",
    "Membership",
    "Applications",
    "Purchases", 
    "Performance",
    "Last Used",
    "Actions",
  ];

  // Calculate summary stats
  const totalApplications = topPerformingCodes.reduce((sum, code) => sum + code.applications, 0);
  const totalPurchases = topPerformingCodes.reduce((sum, code) => sum + code.purchases, 0);
  const averageConversionRate = topPerformingCodes.length > 0 
    ? topPerformingCodes.reduce((sum, code) => sum + code.conversionRate, 0) / topPerformingCodes.length 
    : 0;

  return (
    <Card>
      <Box padding="400">
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text variant="headingMd" as="h3">
              Top Performing Invite Codes
            </Text>
            <Badge tone="info">
              Top {topPerformingCodes.length}
            </Badge>
          </InlineStack>

          {/* Summary Stats */}
          <Box padding="300" background="bg-surface-secondary" borderRadius="200">
            <InlineStack gap="600" align="start">
              <BlockStack gap="100">
                <Text variant="bodySm" tone="subdued">Total Applications</Text>
                <Text variant="bodyMd" fontWeight="medium">{totalApplications.toLocaleString()}</Text>
              </BlockStack>
              <BlockStack gap="100">
                <Text variant="bodySm" tone="subdued">Total Purchases</Text>
                <Text variant="bodyMd" fontWeight="medium">{totalPurchases.toLocaleString()}</Text>
              </BlockStack>
              <BlockStack gap="100">
                <Text variant="bodySm" tone="subdued">Average Conversion</Text>
                <Text variant="bodyMd" fontWeight="medium">{averageConversionRate.toFixed(1)}%</Text>
              </BlockStack>
            </InlineStack>
          </Box>
          
          <DataTable
            columnContentTypes={[
              "text",
              "text",
              "numeric",
              "numeric",
              "text",
              "text",
              "text",
            ]}
            headings={headings}
            rows={rows}
            footerContent={
              <Text variant="bodySm" tone="subdued">
                Showing top {topPerformingCodes.length} codes ranked by purchases and conversion rate
              </Text>
            }
          />
        </BlockStack>
      </Box>
    </Card>
  );
}
