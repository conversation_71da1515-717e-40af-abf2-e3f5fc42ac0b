import { PrismaClient } from "@prisma/client";
import type { InviteC<PERSON>, Usage, Partner } from "@prisma/client";

const db = new PrismaClient();

export type InviteCodeWithUsages = InviteCode & {
  usages: Usage[];
  partner?: Partner | null;
};

export interface CreateInviteCodeData {
  code?: string; // Make code optional - will generate if not provided
  maxUses?: number;
  expiresAt?: Date;
  createdBy: string;
  shopId: string;
  partnerId?: number; // Optional partner association
  membershipId: string; // Required membership from Appstle
}

export interface InviteCodeFilters {
  shopId: string;
  isActive?: boolean;
  search?: string;
  partnerId?: number;
  membershipId?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  page?: number;
  limit?: number;
}

// Generate a random invite code
export function generateInviteCode(length: number = 8): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Generate a unique invite code (checks database for duplicates)
export async function generateUniqueInviteCode(length: number = 8, maxAttempts: number = 10): Promise<string> {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const code = generateInviteCode(length);

    // Check if code already exists
    const existingCode = await getInviteCodeByCode(code);
    if (!existingCode) {
      console.log(`✅ Generated unique code: ${code} (attempt ${attempt + 1})`);
      return code;
    }

    console.log(`⚠️ Code ${code} already exists, trying again... (attempt ${attempt + 1})`);
  }

  // If we couldn't generate a unique code after maxAttempts, throw an error
  throw new Error(`Failed to generate unique invite code after ${maxAttempts} attempts`);
}

// Create a new invite code
export async function createInviteCode(data: CreateInviteCodeData): Promise<InviteCode> {
  // Generate a unique code if none provided
  const code = data.code || await generateUniqueInviteCode();

  // If a code was provided, check if it already exists
  if (data.code) {
    const existingCode = await getInviteCodeByCode(data.code);
    if (existingCode) {
      throw new Error(`Invite code '${data.code}' already exists`);
    }
  }

  return db.inviteCode.create({
    data: {
      code,
      maxUses: data.maxUses || 1,
      expiresAt: data.expiresAt,
      createdBy: data.createdBy,
      shopId: data.shopId,
      partnerId: data.partnerId,
      membershipId: data.membershipId,
    },
  });
}

// Get invite codes with pagination and filtering
export async function getInviteCodes(filters: InviteCodeFilters) {
  const {
    shopId,
    isActive,
    search,
    partnerId,
    membershipId,
    dateFrom,
    dateTo,
    sortBy = "newest",
    page = 1,
    limit = 20
  } = filters;
  const skip = (page - 1) * limit;

  const where: any = {
    shopId,
    ...(isActive !== undefined && { isActive }),
    ...(search && {
      code: {
        contains: search,
        mode: "insensitive" as const,
      },
    }),
    ...(partnerId && { partnerId }),
    ...(membershipId && { membershipId }),
  };

  // Add date range filtering
  if (dateFrom || dateTo) {
    where.createdAt = {};
    if (dateFrom) {
      where.createdAt.gte = new Date(dateFrom);
    }
    if (dateTo) {
      // Add one day to include the entire end date
      const endDate = new Date(dateTo);
      endDate.setDate(endDate.getDate() + 1);
      where.createdAt.lt = endDate;
    }
  }

  // Determine sort order
  let orderBy: any = { createdAt: "desc" }; // Default to newest

  switch (sortBy) {
    case "oldest":
      orderBy = { createdAt: "asc" };
      break;
    case "mostUsed":
      // Sort by actual usage count (usages relation), then by creation date for ties
      orderBy = [
        { usages: { _count: "desc" } },
        { createdAt: "desc" }
      ];
      break;
    case "leastUsed":
      // Sort by actual usage count (usages relation), then by creation date for ties
      orderBy = [
        { usages: { _count: "asc" } },
        { createdAt: "desc" }
      ];
      break;
    case "expiringSoon":
      orderBy = { expiresAt: "asc" };
      break;
    case "newest":
    default:
      orderBy = { createdAt: "desc" };
      break;
  }

  const [codes, total] = await Promise.all([
    db.inviteCode.findMany({
      where,
      include: {
        usages: {
          orderBy: { usedAt: "desc" },
          take: 5, // Include last 5 usages for preview
        },
        partner: true, // Include partner information
      },
      orderBy,
      skip,
      take: limit,
    }),
    db.inviteCode.count({ where }),
  ]);

  return {
    codes,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  };
}

// Get a single invite code by ID
export async function getInviteCodeById(id: number, shopId: string): Promise<InviteCodeWithUsages | null> {
  return db.inviteCode.findFirst({
    where: { id, shopId },
    include: {
      usages: {
        orderBy: { usedAt: "desc" },
      },
      partner: true, // Include partner information
    },
  });
}

// Get invite code by code string for validation
export async function getInviteCodeByCode(code: string): Promise<InviteCode | null> {
  return db.inviteCode.findUnique({
    where: { code },
  });
}

// Update invite code
export async function updateInviteCode(
  id: number,
  shopId: string,
  data: Partial<Pick<InviteCode, "code" | "maxUses" | "expiresAt" | "isActive" | "partnerId" | "membershipId">>
): Promise<InviteCode> {
  return db.inviteCode.update({
    where: { id, shopId },
    data,
  });
}

// Delete invite code
export async function deleteInviteCode(id: number, shopId: string): Promise<void> {
  await db.inviteCode.delete({
    where: { id, shopId },
  });
}

// Increment purchases count for an invite code
export async function incrementPurchasesCount(id: number, shopId: string): Promise<InviteCode> {
  console.log(`📈 Incrementing purchases count for invite code ${id}`);

  return db.inviteCode.update({
    where: { id, shopId },
    data: {
      purchasesCount: {
        increment: 1,
      },
    },
  });
}

// Increment applications count for an invite code
export async function incrementApplicationsCount(id: number, shopId: string): Promise<InviteCode> {
  console.log(`📈 Incrementing applications count for invite code ${id}`);

  return db.inviteCode.update({
    where: { id, shopId },
    data: {
      applicationsCount: {
        increment: 1,
      },
    },
  });
}

// Increment purchases count by code string
export async function incrementPurchasesCountByCode(code: string): Promise<InviteCode | null> {
  console.log(`📈 Incrementing purchases count for code: ${code}`);

  try {
    return await db.inviteCode.update({
      where: { code },
      data: {
        purchasesCount: {
          increment: 1,
        },
      },
    });
  } catch (error) {
    console.error(`❌ Error incrementing purchases count for code ${code}:`, error);
    return null;
  }
}

// Increment applications count by code string
export async function incrementApplicationsCountByCode(code: string): Promise<InviteCode | null> {
  console.log(`📈 Incrementing applications count for code: ${code}`);

  try {
    return await db.inviteCode.update({
      where: { code },
      data: {
        applicationsCount: {
          increment: 1,
        },
      },
    });
  } catch (error) {
    console.error(`❌ Error incrementing applications count for code ${code}:`, error);
    return null;
  }
}

// Validate and use invite code
export async function validateAndUseInviteCode(
  code: string,
  metadata?: {
    customerId?: string;
    ipAddress?: string;
    userAgent?: string;
  }
): Promise<{ valid: boolean; reason?: string; inviteCode?: InviteCode }> {
  const inviteCode = await getInviteCodeByCode(code);

  if (!inviteCode) {
    return { valid: false, reason: "Code not found" };
  }

  if (!inviteCode.isActive) {
    return { valid: false, reason: "Code is inactive" };
  }

  if (inviteCode.expiresAt && inviteCode.expiresAt < new Date()) {
    return { valid: false, reason: "Code has expired" };
  }

  if (inviteCode.usedCount >= inviteCode.maxUses) {
    return { valid: false, reason: "Code has reached maximum uses" };
  }

  // Record the usage and increment the used count
  await db.$transaction([
    db.usage.create({
      data: {
        inviteCodeId: inviteCode.id,
        customerId: metadata?.customerId,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
      },
    }),
    db.inviteCode.update({
      where: { id: inviteCode.id },
      data: { usedCount: { increment: 1 } },
    }),
  ]);

  return { valid: true, inviteCode };
}

// Validate and use invite code with membership tag application
export async function validateAndUseInviteCodeWithMembership(
  code: string,
  metadata?: {
    customerId?: string;
    ipAddress?: string;
    userAgent?: string;
    shopId?: string;
    admin?: any;
  }
): Promise<{ valid: boolean; reason?: string; inviteCode?: InviteCode; membershipApplied?: boolean }> {
  const result = await validateAndUseInviteCode(code, metadata);

  if (!result.valid || !result.inviteCode) {
    return result;
  }

  let membershipApplied = false;

  // Apply membership tag if customer ID and admin context are provided
  if (metadata?.customerId && metadata?.shopId && metadata?.admin && result.inviteCode.membershipId) {
    try {
      console.log(`🏷️ Applying membership tag for invite code ${code} to customer ${metadata.customerId}`);

      const { applyMembershipTagToCustomer } = await import("../services/customer-tagging.server");

      const membershipResult = await applyMembershipTagToCustomer({
        customerId: metadata.customerId,
        membershipId: result.inviteCode.membershipId,
        shop: metadata.shopId,
        admin: metadata.admin,
      });

      if (membershipResult.success) {
        console.log(`✅ Membership tag applied successfully: ${membershipResult.message}`);
        membershipApplied = true;
      } else {
        console.log(`⚠️ Failed to apply membership tag: ${membershipResult.message}`);
      }
    } catch (error: any) {
      console.error(`❌ Error applying membership tag:`, error);
      // Don't fail the invite code validation if membership tagging fails
    }
  }

  return {
    valid: true,
    inviteCode: result.inviteCode,
    membershipApplied
  };
}

// Get usage statistics for dashboard
export async function getUsageStats(shopId: string) {
  const [totalCodes, activeCodes, totalUsages, recentUsages] = await Promise.all([
    db.inviteCode.count({ where: { shopId } }),
    db.inviteCode.count({ where: { shopId, isActive: true } }),
    db.usage.count({
      where: {
        inviteCode: { shopId },
      },
    }),
    db.usage.findMany({
      where: {
        inviteCode: { shopId },
      },
      include: {
        inviteCode: {
          select: { code: true },
        },
      },
      orderBy: { usedAt: "desc" },
      take: 10,
    }),
  ]);

  return {
    totalCodes,
    activeCodes,
    totalUsages,
    recentUsages,
  };
}
