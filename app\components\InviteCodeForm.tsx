import { useState, useCallback } from "react";
import {
  Card,
  FormLayout,
  TextField,
  Select,
  Checkbox,
  Button,
  InlineStack,
  Box,
  Text,
  Banner,
  Modal,
} from "@shopify/polaris";
import { PlusIcon } from "@shopify/polaris-icons";

// Client-side code generation function
function generateInviteCode(length: number = 8): string {
  // Only generate on client side to avoid hydration mismatches
  if (typeof window === 'undefined') return '';

  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Format date and time for datetime-local input (YYYY-MM-DDTHH:MM)
function formatDateTimeForInput(dateString: string): string {
  if (!dateString) return "";
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return "";
  // Get local timezone offset and adjust
  const localDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);
  return localDate.toISOString().slice(0, 16);
}

interface InviteCodeFormProps {
  initialData?: {
    code?: string;
    maxUses?: number;
    expiresAt?: string;
    isActive?: boolean;
    partnerId?: number;
    membershipId?: string;
  };
  onSubmit: (data: InviteCodeFormData) => void;
  onCancel?: () => void;
  loading?: boolean;
  error?: string;
  mode?: "create" | "edit";
  membershipPlans?: Array<{ value: string; label: string }>;
  partners?: Array<{ value: string; label: string }>;
  onCreatePartner?: (partnerData: { name: string; email?: string; company?: string }) => Promise<void>;
}

export interface InviteCodeFormData {
  code: string;
  maxUses: number;
  expiresAt?: string;
  isActive: boolean;
  partnerId?: number;
  membershipId: string;
}

export function InviteCodeForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  error,
  mode = "create",
  membershipPlans = [],
  partners = [],
  onCreatePartner,
}: InviteCodeFormProps) {
  const [formData, setFormData] = useState<InviteCodeFormData>({
    code: initialData?.code || "",
    maxUses: initialData?.maxUses || 1,
    expiresAt: initialData?.expiresAt || "",
    isActive: initialData?.isActive ?? true,
    partnerId: initialData?.partnerId,
    membershipId: initialData?.membershipId || "",
  });

  const [errors, setErrors] = useState<Partial<Record<keyof InviteCodeFormData, string>>>({});
  const [showCreatePartnerModal, setShowCreatePartnerModal] = useState(false);
  const [newPartnerData, setNewPartnerData] = useState({
    name: "",
    email: "",
    company: "",
  });
  const [creatingPartner, setCreatingPartner] = useState(false);

  const validateForm = useCallback(() => {
    const newErrors: Partial<Record<keyof InviteCodeFormData, string>> = {};

    if (!formData.code.trim()) {
      newErrors.code = "Invite code is required";
    } else if (formData.code.length < 3) {
      newErrors.code = "Invite code must be at least 3 characters";
    } else if (!/^[A-Z0-9]+$/.test(formData.code)) {
      newErrors.code = "Invite code can only contain uppercase letters and numbers";
    }

    if (formData.maxUses < 1) {
      newErrors.maxUses = "Maximum uses must be at least 1";
    } else if (formData.maxUses > 999999) {
      newErrors.maxUses = "Maximum uses cannot exceed 999,999";
    }

    if (formData.expiresAt) {
      const expiryDate = new Date(formData.expiresAt);
      const now = new Date();
      if (expiryDate <= now) {
        newErrors.expiresAt = "Expiry date must be in the future";
      }
    }

    if (!formData.membershipId.trim()) {
      newErrors.membershipId = "Membership plan is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSubmit = useCallback(() => {
    if (validateForm()) {
      onSubmit(formData);
    }
  }, [formData, validateForm, onSubmit]);

  const handleFieldChange = useCallback(
    (field: keyof InviteCodeFormData) => (value: string | number | boolean | undefined) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: undefined }));
      }
    },
    [errors]
  );

  const generateRandomCode = useCallback(() => {
    const newCode = generateInviteCode(8);
    setFormData((prev) => ({ ...prev, code: newCode }));
    if (errors.code) {
      setErrors((prev) => ({ ...prev, code: undefined }));
    }
  }, [errors.code]);

  const handleCreatePartner = useCallback(async () => {
    if (!newPartnerData.name.trim()) {
      return;
    }

    setCreatingPartner(true);
    try {
      if (onCreatePartner) {
        await onCreatePartner(newPartnerData);
        setShowCreatePartnerModal(false);
        setNewPartnerData({ name: "", email: "", company: "" });
      }
    } catch (error) {
      console.error("Error creating partner:", error);
    } finally {
      setCreatingPartner(false);
    }
  }, [newPartnerData, onCreatePartner]);

  const maxUsesOptions = [
    { label: "1 use", value: "1" },
    { label: "5 uses", value: "5" },
    { label: "10 uses", value: "10" },
    { label: "25 uses", value: "25" },
    { label: "50 uses", value: "50" },
    { label: "100 uses", value: "100" },
    { label: "Custom", value: "custom" },
  ];

  const isCustomMaxUses = !maxUsesOptions.some(option => 
    option.value !== "custom" && parseInt(option.value) === formData.maxUses
  );

  return (
    <>
      <Card>
      <Box padding="400">
        <Text variant="headingMd" as="h2">
          {mode === "create" ? "Create New Invite Code" : "Edit Invite Code"}
        </Text>

        {error && (
          <Box paddingBlockStart="400">
            <Banner tone="critical">
              <p>{error}</p>
            </Banner>
          </Box>
        )}

        <Box paddingBlockStart="400">
          <FormLayout>
            <FormLayout.Group>
              <TextField
                label="Invite Code"
                value={formData.code}
                onChange={handleFieldChange("code")}
                error={errors.code}
                placeholder="Enter invite code"
                autoComplete="off"
                helpText="Use uppercase letters and numbers only"
                connectedRight={
                  <Button
                    variant="secondary"
                    onClick={generateRandomCode}
                    disabled={loading}
                  >
                    Generate
                  </Button>
                }
              />
            </FormLayout.Group>

            <FormLayout.Group>
              <Select
                label="Maximum Uses"
                options={maxUsesOptions}
                value={isCustomMaxUses ? "custom" : formData.maxUses.toString()}
                onChange={(value) => {
                  if (value !== "custom") {
                    handleFieldChange("maxUses")(parseInt(value));
                  }
                }}
              />
              
              {isCustomMaxUses && (
                <TextField
                  label="Custom Maximum Uses"
                  type="number"
                  value={formData.maxUses.toString()}
                  onChange={(value) => handleFieldChange("maxUses")(parseInt(value) || 1)}
                  error={errors.maxUses}
                  min={1}
                  max={999999}
                  autoComplete="off"
                />
              )}
            </FormLayout.Group>

            <TextField
              label="Expiry Date & Time (Optional)"
              type="datetime-local"
              value={formData.expiresAt ? formatDateTimeForInput(formData.expiresAt) : ""}
              onChange={handleFieldChange("expiresAt")}
              error={errors.expiresAt}
              helpText="Format: YYYY-MM-DD HH:MM (24-hour format) - Leave empty for no expiry"
              autoComplete="off"
              placeholder="2024-12-25T23:59"
            />

            <Select
              label="Assign Membership *"
              options={[
                { label: "Select a membership plan...", value: "" },
                ...membershipPlans,
              ]}
              value={formData.membershipId}
              onChange={handleFieldChange("membershipId")}
              error={errors.membershipId}
              helpText="Choose the membership plan for this invite code"
            />

            <FormLayout.Group>
              <Select
                label="Assign Partner (Optional)"
                options={[
                  { label: "No partner assigned", value: "" },
                  ...partners,
                ]}
                value={formData.partnerId?.toString() || ""}
                onChange={(value) => handleFieldChange("partnerId")(value ? parseInt(value) : undefined)}
                helpText="Optionally assign this code to a partner"
              />

              {onCreatePartner && (
                <Box paddingBlockStart="600">
                  <Button
                    variant="secondary"
                    icon={PlusIcon}
                    onClick={() => setShowCreatePartnerModal(true)}
                    disabled={loading}
                  >
                    Create new partner
                  </Button>
                </Box>
              )}
            </FormLayout.Group>

            <Checkbox
              label="Active"
              checked={formData.isActive}
              onChange={handleFieldChange("isActive")}
              helpText="Inactive codes cannot be used"
            />

            <InlineStack align="end" gap="300">
              {onCancel && (
                <Button
                  variant="secondary"
                  onClick={onCancel}
                  disabled={loading}
                >
                  Cancel
                </Button>
              )}
              <Button
                variant="primary"
                onClick={handleSubmit}
                loading={loading}
                disabled={loading}
              >
                {mode === "create" ? "Create Code" : "Update Code"}
              </Button>
            </InlineStack>
          </FormLayout>
        </Box>
      </Box>
      </Card>

      {/* Create Partner Modal */}
    {showCreatePartnerModal && (
      <Modal
        open={showCreatePartnerModal}
        onClose={() => setShowCreatePartnerModal(false)}
        title="Create New Partner"
        primaryAction={{
          content: "Create Partner",
          onAction: handleCreatePartner,
          loading: creatingPartner,
          disabled: !newPartnerData.name.trim() || creatingPartner,
        }}
        secondaryActions={[
          {
            content: "Cancel",
            onAction: () => setShowCreatePartnerModal(false),
            disabled: creatingPartner,
          },
        ]}
      >
        <Modal.Section>
          <FormLayout>
            <TextField
              label="Partner Name *"
              value={newPartnerData.name}
              onChange={(value) => setNewPartnerData(prev => ({ ...prev, name: value }))}
              placeholder="Enter partner name"
              autoComplete="off"
              disabled={creatingPartner}
            />
            <TextField
              label="Email (Optional)"
              type="email"
              value={newPartnerData.email}
              onChange={(value) => setNewPartnerData(prev => ({ ...prev, email: value }))}
              placeholder="<EMAIL>"
              autoComplete="off"
              disabled={creatingPartner}
            />
            <TextField
              label="Company (Optional)"
              value={newPartnerData.company}
              onChange={(value) => setNewPartnerData(prev => ({ ...prev, company: value }))}
              placeholder="Company name"
              autoComplete="off"
              disabled={creatingPartner}
            />
          </FormLayout>
        </Modal.Section>
      </Modal>
    )}
    </>
  );
}

// Quick Create Form for Dashboard
interface QuickCreateFormProps {
  onSubmit: (data: { code: string; maxUses: number }) => void;
  loading?: boolean;
}

export function QuickCreateForm({ onSubmit, loading = false }: QuickCreateFormProps) {
  const [code, setCode] = useState("");
  const [maxUses, setMaxUses] = useState(1);

  const handleQuickCreate = useCallback(() => {
    if (code.trim()) {
      onSubmit({ code: code.trim().toUpperCase(), maxUses });
      setCode("");
      setMaxUses(1);
    }
  }, [code, maxUses, onSubmit]);

  const handleGenerateAndCreate = useCallback(() => {
    const newCode = generateInviteCode(8);
    onSubmit({ code: newCode, maxUses });
    setMaxUses(1);
  }, [maxUses, onSubmit]);

  return (
    <Card>
      <Box padding="400">
        <div className="dashboard-card fade-in">
          <Text variant="headingMd" as="h3">
            Quick Create
          </Text>

          <Box paddingBlockStart="400">
            <FormLayout>
              <FormLayout.Group>
                <TextField
                  label="Code"
                  value={code}
                  onChange={setCode}
                  placeholder="Enter code or generate"
                  autoComplete="off"
                />
                <Select
                  label="Max Uses"
                  options={[
                    { label: "1", value: "1" },
                    { label: "5", value: "5" },
                    { label: "10", value: "10" },
                    { label: "25", value: "25" },
                  ]}
                  value={maxUses.toString()}
                  onChange={(value) => setMaxUses(parseInt(value))}
                />
              </FormLayout.Group>

              <InlineStack gap="200">
                <div className="hover-lift">
                  <Button
                    variant="secondary"
                    onClick={handleGenerateAndCreate}
                    loading={loading}
                    disabled={loading}
                  >
                    Generate & Create
                  </Button>
                </div>
                <div className="hover-lift">
                  <Button
                    variant="primary"
                    onClick={handleQuickCreate}
                    loading={loading}
                    disabled={loading || !code.trim()}
                  >
                    Create
                  </Button>
                </div>
              </InlineStack>
            </FormLayout>
          </Box>
        </div>
      </Box>
    </Card>
  );
}
