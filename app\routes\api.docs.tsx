import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const baseUrl = `${url.protocol}//${url.host}`;
  
  return json({
    baseUrl,
    apiVersion: "1.0.0",
    lastUpdated: "2025-06-23"
  });
};

export default function ApiDocs() {
  return (
    <div style={{ 
      maxWidth: "1200px", 
      margin: "0 auto", 
      padding: "40px 20px",
      fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
      lineHeight: "1.6",
      color: "#333"
    }}>
      <header style={{ marginBottom: "40px", textAlign: "center" }}>
        <h1 style={{ fontSize: "36px", marginBottom: "10px", color: "#2c3e50" }}>
          Invite Code Validation API
        </h1>
        <p style={{ fontSize: "18px", color: "#7f8c8d" }}>
          RESTful API for validating invite codes with Shopify Multipass integration
        </p>
      </header>

      <section style={{ marginBottom: "40px" }}>
        <h2 style={{ fontSize: "24px", marginBottom: "20px", color: "#34495e" }}>
          Overview
        </h2>
        <p>
          The Invite Code Validation API allows you to validate invite codes and optionally 
          generate Shopify Multipass login URLs for seamless customer authentication.
        </p>
        <div style={{ 
          background: "#f8f9fa", 
          padding: "20px", 
          borderRadius: "8px",
          border: "1px solid #e9ecef",
          marginTop: "20px"
        }}>
          <h3 style={{ margin: "0 0 10px 0", color: "#495057" }}>Base URL</h3>
          <code style={{ 
            background: "#e9ecef", 
            padding: "4px 8px", 
            borderRadius: "4px",
            fontFamily: "Monaco, Consolas, monospace"
          }}>
            https://your-app-domain.com/api/validate-invite
          </code>
        </div>
      </section>

      <section style={{ marginBottom: "40px" }}>
        <h2 style={{ fontSize: "24px", marginBottom: "20px", color: "#34495e" }}>
          Authentication
        </h2>
        <p>
          This is a public API endpoint that doesn't require authentication. However, 
          it includes rate limiting (20 requests per minute per IP address).
        </p>
      </section>

      <section style={{ marginBottom: "40px" }}>
        <h2 style={{ fontSize: "24px", marginBottom: "20px", color: "#34495e" }}>
          Endpoints
        </h2>

        <div style={{ marginBottom: "30px" }}>
          <h3 style={{ fontSize: "20px", marginBottom: "15px", color: "#2c3e50" }}>
            POST /api/validate-invite
          </h3>
          <p>Validates an invite code and optionally generates a Multipass login URL.</p>
          
          <h4 style={{ fontSize: "16px", marginTop: "20px", marginBottom: "10px" }}>Request Body</h4>
          <pre style={{ 
            background: "#f8f9fa", 
            padding: "20px", 
            borderRadius: "8px",
            overflow: "auto",
            border: "1px solid #e9ecef",
            fontFamily: "Monaco, Consolas, monospace",
            fontSize: "14px"
          }}>
{`{
  "code": "WELCOME10",
  "shopDomain": "your-shop.myshopify.com",
  "customerData": {
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "returnTo": "/collections/sale"
  },
  "metadata": {
    "customerId": "customer_123",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0..."
  }
}`}
          </pre>

          <h4 style={{ fontSize: "16px", marginTop: "20px", marginBottom: "10px" }}>Response (Success)</h4>
          <pre style={{ 
            background: "#d4edda", 
            padding: "20px", 
            borderRadius: "8px",
            overflow: "auto",
            border: "1px solid #c3e6cb",
            fontFamily: "Monaco, Consolas, monospace",
            fontSize: "14px"
          }}>
{`{
  "success": true,
  "message": "Invite code validated successfully",
  "data": {
    "code": "WELCOME10",
    "remainingUses": 95,
    "maxUses": 100,
    "expiresAt": "2025-07-23T10:30:00.000Z",
    "loginUrl": "https://your-shop.myshopify.com/account/login/multipass/..."
  }
}`}
          </pre>

          <h4 style={{ fontSize: "16px", marginTop: "20px", marginBottom: "10px" }}>Response (Error)</h4>
          <pre style={{ 
            background: "#f8d7da", 
            padding: "20px", 
            borderRadius: "8px",
            overflow: "auto",
            border: "1px solid #f5c6cb",
            fontFamily: "Monaco, Consolas, monospace",
            fontSize: "14px"
          }}>
{`{
  "success": false,
  "message": "Code has expired",
  "error": "INVALID_CODE"
}`}
          </pre>
        </div>

        <div style={{ marginBottom: "30px" }}>
          <h3 style={{ fontSize: "20px", marginBottom: "15px", color: "#2c3e50" }}>
            GET /api/validate-invite
          </h3>
          <p>Alternative method using query parameters for simple integrations.</p>
          
          <h4 style={{ fontSize: "16px", marginTop: "20px", marginBottom: "10px" }}>Query Parameters</h4>
          <pre style={{ 
            background: "#f8f9fa", 
            padding: "20px", 
            borderRadius: "8px",
            overflow: "auto",
            border: "1px solid #e9ecef",
            fontFamily: "Monaco, Consolas, monospace",
            fontSize: "14px"
          }}>
{`GET /api/validate-invite?code=WELCOME10&shopDomain=your-shop.myshopify.com&email=<EMAIL>&firstName=John&lastName=Doe&returnTo=/collections/sale`}
          </pre>
        </div>
      </section>

      <section style={{ marginBottom: "40px" }}>
        <h2 style={{ fontSize: "24px", marginBottom: "20px", color: "#34495e" }}>
          Error Codes
        </h2>
        <table style={{ 
          width: "100%", 
          borderCollapse: "collapse",
          border: "1px solid #dee2e6"
        }}>
          <thead>
            <tr style={{ background: "#f8f9fa" }}>
              <th style={{ padding: "12px", textAlign: "left", border: "1px solid #dee2e6" }}>Error Code</th>
              <th style={{ padding: "12px", textAlign: "left", border: "1px solid #dee2e6" }}>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ padding: "12px", border: "1px solid #dee2e6", fontFamily: "monospace" }}>MISSING_CODE</td>
              <td style={{ padding: "12px", border: "1px solid #dee2e6" }}>Invite code parameter is required</td>
            </tr>
            <tr style={{ background: "#f8f9fa" }}>
              <td style={{ padding: "12px", border: "1px solid #dee2e6", fontFamily: "monospace" }}>INVALID_CODE</td>
              <td style={{ padding: "12px", border: "1px solid #dee2e6" }}>Code not found, expired, inactive, or fully used</td>
            </tr>
            <tr>
              <td style={{ padding: "12px", border: "1px solid #dee2e6", fontFamily: "monospace" }}>RATE_LIMIT_EXCEEDED</td>
              <td style={{ padding: "12px", border: "1px solid #dee2e6" }}>Too many requests from this IP address</td>
            </tr>
            <tr style={{ background: "#f8f9fa" }}>
              <td style={{ padding: "12px", border: "1px solid #dee2e6", fontFamily: "monospace" }}>INVALID_JSON</td>
              <td style={{ padding: "12px", border: "1px solid #dee2e6" }}>Request body contains invalid JSON</td>
            </tr>
            <tr>
              <td style={{ padding: "12px", border: "1px solid #dee2e6", fontFamily: "monospace" }}>INTERNAL_ERROR</td>
              <td style={{ padding: "12px", border: "1px solid #dee2e6" }}>Server error occurred</td>
            </tr>
          </tbody>
        </table>
      </section>

      <section style={{ marginBottom: "40px" }}>
        <h2 style={{ fontSize: "24px", marginBottom: "20px", color: "#34495e" }}>
          Integration Examples
        </h2>

        <h3 style={{ fontSize: "18px", marginBottom: "15px", color: "#2c3e50" }}>JavaScript/Fetch</h3>
        <pre style={{ 
          background: "#f8f9fa", 
          padding: "20px", 
          borderRadius: "8px",
          overflow: "auto",
          border: "1px solid #e9ecef",
          fontFamily: "Monaco, Consolas, monospace",
          fontSize: "14px"
        }}>
{`async function validateInviteCode(code, shopDomain, customerData) {
  try {
    const response = await fetch('/api/validate-invite', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code,
        shopDomain,
        customerData
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data.loginUrl) {
      // Redirect to Shopify with Multipass
      window.location.href = result.data.loginUrl;
    } else {
      console.error('Validation failed:', result.message);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
}`}
        </pre>

        <h3 style={{ fontSize: "18px", marginBottom: "15px", marginTop: "30px", color: "#2c3e50" }}>cURL</h3>
        <pre style={{ 
          background: "#f8f9fa", 
          padding: "20px", 
          borderRadius: "8px",
          overflow: "auto",
          border: "1px solid #e9ecef",
          fontFamily: "Monaco, Consolas, monospace",
          fontSize: "14px"
        }}>
{`curl -X POST https://your-app-domain.com/api/validate-invite \\
  -H "Content-Type: application/json" \\
  -d '{
    "code": "WELCOME10",
    "shopDomain": "your-shop.myshopify.com",
    "customerData": {
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe"
    }
  }'`}
        </pre>
      </section>

      <section>
        <h2 style={{ fontSize: "24px", marginBottom: "20px", color: "#34495e" }}>
          Rate Limiting
        </h2>
        <p>
          The API implements rate limiting to prevent abuse:
        </p>
        <ul>
          <li><strong>Limit:</strong> 20 requests per minute per IP address</li>
          <li><strong>Response:</strong> HTTP 429 when limit exceeded</li>
          <li><strong>Reset:</strong> Limit resets every minute</li>
        </ul>
      </section>
    </div>
  );
}
