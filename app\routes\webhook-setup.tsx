import type { LoaderFunctionArgs } from "@remix-run/node";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const baseUrl = new URL(request.url).origin;
  
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopify Webhook Setup - Supertested Invite Codes</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        h3 { color: #7f8c8d; }
        .webhook-url { 
            background: #2c3e50; 
            color: #ecf0f1; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace;
            font-size: 16px;
            margin: 15px 0;
            word-break: break-all;
        }
        .step {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            padding: 20px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffc107;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        code { 
            background: #f1c40f; 
            padding: 2px 6px; 
            border-radius: 3px;
            color: #2c3e50;
        }
        pre { 
            background: #2c3e50; 
            color: #ecf0f1; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #f2f2f2;
            font-weight: bold;
        }
        .tag-example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Shopify Webhook Setup</h1>
        <p>Configure your Shopify store to automatically track invite code sales and tag orders with partner information.</p>

        <h2>📍 Webhook URL</h2>
        <div class="webhook-url">
            ${baseUrl}/webhooks/orders/paid
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> This webhook URL must be accessible from the internet. If you're using a local development server, use a tool like ngrok or deploy to a public server.
        </div>

        <h2>🛠️ Setup Instructions</h2>

        <div class="step">
            <h3>Step 1: Access Shopify Admin</h3>
            <ol>
                <li>Log in to your Shopify admin panel</li>
                <li>Go to <strong>Settings</strong> → <strong>Notifications</strong></li>
                <li>Scroll down to the <strong>Webhooks</strong> section</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 2: Create Webhook</h3>
            <ol>
                <li>Click <strong>"Create webhook"</strong></li>
                <li>Set <strong>Event</strong> to: <code>Order payment</code></li>
                <li>Set <strong>Format</strong> to: <code>JSON</code></li>
                <li>Set <strong>URL</strong> to: <code>${baseUrl}/webhooks/orders/paid</code></li>
                <li>Set <strong>API version</strong> to: <code>2025-01</code> (or latest)</li>
                <li>Click <strong>"Save webhook"</strong></li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 3: Test the Webhook</h3>
            <ol>
                <li>Create a test order in your Shopify store</li>
                <li>Add an invite code to the order (see methods below)</li>
                <li>Complete the payment</li>
                <li>Check your analytics dashboard for the tracked sale</li>
            </ol>
        </div>

        <h2>🏷️ Order Tagging System</h2>
        <p>When an order is processed with an invite code, the webhook automatically applies comprehensive tags:</p>

        <h3>Order Tags Applied</h3>
        <table>
            <tr><th>Tag Type</th><th>Format</th><th>Example</th></tr>
            <tr><td>Main Order Tag</td><td><code>INVITECODE-PARTNERNAME</code></td><td><code>WELCOME10-ALPHAMARKETING</code></td></tr>
            <tr><td>Invite Code Tag</td><td><code>invite-code-{code}</code></td><td><code>invite-code-welcome10</code></td></tr>
            <tr><td>Partner Tag</td><td><code>partner-{name}</code></td><td><code>partner-alphamarketing</code></td></tr>
            <tr><td>Membership Tag</td><td><code>membership-{type}</code></td><td><code>membership-premium</code></td></tr>
            <tr><td>Tracking Tag</td><td><code>analytics-tracking</code></td><td><code>analytics-tracking</code></td></tr>
        </table>

        <h3>Customer Tags Applied</h3>
        <table>
            <tr><th>Tag Type</th><th>Format</th><th>Example</th></tr>
            <tr><td>Invite Tag</td><td><code>invite-{code}</code></td><td><code>invite-welcome10</code></td></tr>
            <tr><td>Partner Tag</td><td><code>partner-{name}</code></td><td><code>partner-alphamarketing</code></td></tr>
            <tr><td>Membership Tag</td><td><code>membership-{type}</code></td><td><code>membership-premium</code></td></tr>
            <tr><td>Customer Type</td><td><code>invite-code-customer</code></td><td><code>invite-code-customer</code></td></tr>
        </table>

        <h3>Order Attributes Added</h3>
        <ul>
            <li><code>invite_code</code>: The invite code used</li>
            <li><code>partner_name</code>: Name of the partner</li>
            <li><code>partner_id</code>: Partner ID in the system</li>
            <li><code>membership_id</code>: Membership type</li>
            <li><code>order_tag</code>: Main order tag (INVITECODE-PARTNERNAME)</li>
            <li><code>processed_by_webhook</code>: Confirmation of webhook processing</li>
            <li><code>processed_at</code>: Timestamp of processing</li>
        </ul>

        <h2>📝 How to Add Invite Codes to Orders</h2>
        <p>The webhook can detect invite codes from multiple sources:</p>

        <h3>Method 1: Order Note Attributes (Recommended)</h3>
        <div class="tag-example">
            // Add to checkout form or cart
            &lt;input type="hidden" name="attributes[invite_code]" value="WELCOME10" /&gt;
        </div>

        <h3>Method 2: Order Notes</h3>
        <div class="tag-example">
            // In order notes field
            "Customer used invite code: WELCOME10"
        </div>

        <h3>Method 3: Line Item Properties</h3>
        <div class="tag-example">
            // Add to product properties
            properties: [
              { name: "invite_code", value: "WELCOME10" }
            ]
        </div>

        <h3>Method 4: Discount Codes</h3>
        <div class="tag-example">
            // If invite codes are implemented as discount codes
            discount_codes: [
              { code: "WELCOME10", amount: "0.00" }
            ]
        </div>

        <h2>📊 Analytics Integration</h2>
        <p>Once the webhook is configured, all orders with invite codes will:</p>
        <ul>
            <li>✅ Be automatically tagged with partner information</li>
            <li>✅ Update invite code usage statistics</li>
            <li>✅ Appear in the analytics dashboard</li>
            <li>✅ Track revenue by partner and invite code</li>
            <li>✅ Enable commission calculations</li>
        </ul>

        <h2>🔍 Troubleshooting</h2>

        <h3>Webhook Not Triggering</h3>
        <ul>
            <li>Verify the webhook URL is publicly accessible</li>
            <li>Check Shopify's webhook delivery attempts in admin</li>
            <li>Ensure the webhook is set to "Order payment" event</li>
            <li>Verify API version compatibility</li>
        </ul>

        <h3>Orders Not Being Tagged</h3>
        <ul>
            <li>Check that invite codes are properly added to orders</li>
            <li>Verify invite codes exist in your system</li>
            <li>Check webhook logs for error messages</li>
            <li>Ensure invite codes belong to the correct shop</li>
        </ul>

        <h3>Analytics Not Updating</h3>
        <ul>
            <li>Verify webhook is processing successfully</li>
            <li>Check that sales are being recorded</li>
            <li>Refresh the analytics dashboard</li>
            <li>Check date filters in analytics</li>
        </ul>

        <div class="info">
            <strong>💡 Pro Tip:</strong> Use the testing interface at <code>${baseUrl}/test-interface</code> to simulate orders and verify your analytics setup before going live.
        </div>

        <h2>📞 Support</h2>
        <p>If you need help setting up the webhook or troubleshooting issues:</p>
        <ul>
            <li>Check the webhook logs in your application</li>
            <li>Test with the simulation interface first</li>
            <li>Verify all invite codes are properly configured</li>
            <li>Ensure partners are assigned to invite codes</li>
        </ul>

        <div class="warning">
            <strong>🔒 Security Note:</strong> The webhook endpoint is designed to handle Shopify's webhook verification. Ensure your server is properly configured to validate webhook authenticity in production.
        </div>
    </div>
</body>
</html>`;

  return new Response(html, {
    headers: {
      "Content-Type": "text/html",
    },
  });
};
