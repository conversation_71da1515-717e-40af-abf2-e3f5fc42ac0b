# Database Setup Guide

## PostgreSQL Setup for Development

### Option 1: Local PostgreSQL Installation

1. **Install PostgreSQL** (if not already installed):
   - Windows: Download from https://www.postgresql.org/download/windows/
   - macOS: `brew install postgresql`
   - Linux: `sudo apt-get install postgresql postgresql-contrib`

2. **Create Database and User**:
   ```sql
   -- Connect to PostgreSQL as superuser
   psql -U postgres
   
   -- Create database
   CREATE DATABASE supersleek_invite_codes;
   
   -- Create user (optional, for security)
   CREATE USER invite_app WITH PASSWORD 'your_secure_password';
   GRANT ALL PRIVILEGES ON DATABASE supersleek_invite_codes TO invite_app;
   ```

3. **Update .env file**:
   ```env
   DATABASE_URL="postgresql://invite_app:your_secure_password@localhost:5432/supertested_invite_codes?schema=public"
   ```

### Option 2: Docker <PERSON>gre<PERSON> (Recommended for Development)

1. **Create docker-compose.yml**:
   ```yaml
   version: '3.8'
   services:
     postgres:
       image: postgres:15
       environment:
         POSTGRES_DB: supertested_invite_codes
         POSTGRES_USER: invite_app
         POSTGRES_PASSWORD: dev_password
       ports:
         - "5432:5432"
       volumes:
         - postgres_data:/var/lib/postgresql/data
   
   volumes:
     postgres_data:
   ```

2. **Start PostgreSQL**:
   ```bash
   docker-compose up -d
   ```

3. **Update .env file**:
   ```env
   DATABASE_URL="postgresql://invite_app:dev_password@localhost:5432/supertested_invite_codes?schema=public"
   ```

### Option 3: Cloud PostgreSQL (Production-like)

Use services like:
- **Supabase** (recommended): https://supabase.com/
- **Railway**: https://railway.app/
- **Neon**: https://neon.tech/
- **PlanetScale**: https://planetscale.com/

## Database Migration

After setting up PostgreSQL:

1. **Run the initial migration**:
   ```bash
   npx prisma migrate dev --name init
   ```

2. **Generate Prisma client** (if not done):
   ```bash
   npx prisma generate
   ```

3. **View database in Prisma Studio** (optional):
   ```bash
   npx prisma studio
   ```

## Environment Variables

Make sure your `.env` file contains:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/supersleek_invite_codes?schema=public"

# Shopify App (get from Partners Dashboard)
SHOPIFY_API_KEY="your_api_key"
SHOPIFY_API_SECRET="your_api_secret"
SHOPIFY_SCOPES="write_products"
SHOPIFY_APP_URL="https://your-app-url.com"

# Multipass (get from Shopify Admin)
SHOPIFY_MULTIPASS_SECRET="your_multipass_secret"

# Session
SHOPIFY_APP_SESSION_SECRET="your_random_session_secret"

# Development
NODE_ENV="development"
```

## Database Schema Overview

The app uses the following tables:

- **invite_codes**: Main table for storing invite codes
- **usages**: Tracks each use of an invite code
- **Session**: Shopify app session storage (built-in)

## Performance Considerations

- Indexes are created on frequently queried columns
- Connection pooling is configured in the Prisma client
- Pagination is implemented for large datasets
- Soft deletion is used for audit purposes

## Troubleshooting

### Common Issues:

1. **Connection refused**: Check if PostgreSQL is running
2. **Database does not exist**: Create the database first
3. **Permission denied**: Check user permissions
4. **SSL issues**: Add `?sslmode=disable` to connection string for local development

### Useful Commands:

```bash
# Reset database (development only)
npx prisma migrate reset

# View current migrations
npx prisma migrate status

# Deploy migrations (production)
npx prisma migrate deploy

# Seed database (if seed file exists)
npx prisma db seed
```
