import { Card, Text, Box, InlineStack } from "@shopify/polaris";
import {
  CodeIcon,
  CheckCircleIcon,
  ClockIcon,
  ChevronUpIcon
} from "@shopify/polaris-icons";

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  icon?: "ticket" | "check" | "clock" | "trending";
  variant?: "default" | "success" | "warning" | "critical";
}

export function StatsCard({ 
  title, 
  value, 
  subtitle, 
  trend, 
  icon = "ticket",
  variant = "default" 
}: StatsCardProps) {
  
  const getIcon = () => {
    switch (icon) {
      case "check":
        return CheckCircleIcon;
      case "clock":
        return ClockIcon;
      case "trending":
        return ChevronUpIcon;
      default:
        return CodeIcon;
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case "success":
        return "stats-card-success";
      case "warning":
        return "stats-card-warning";
      case "critical":
        return "stats-card-critical";
      default:
        return "stats-card-default";
    }
  };

  const IconComponent = getIcon();

  return (
    <Card>
      <Box padding="400">
        <div className={`stats-card ${getVariantStyles()} dashboard-card fade-in`}>
          <InlineStack align="space-between" blockAlign="start">
            <Box>
              <div className="dashboard-metric-label">
                <Text variant="bodyMd" tone="subdued" as="p">
                  {title}
                </Text>
              </div>
              <div className="dashboard-metric-value">
                <Text variant="heading2xl" as="h3" fontWeight="bold">
                  {value}
                </Text>
              </div>
              {subtitle && (
                <div className="dashboard-metric-label">
                  <Text variant="bodySm" tone="subdued" as="p">
                    {subtitle}
                  </Text>
                </div>
              )}
              {trend && (
                <Box paddingBlockStart="200">
                  <InlineStack gap="100" align="start" blockAlign="center">
                    <div className={`trend-indicator ${trend.isPositive ? 'trend-positive' : 'trend-negative'}`}>
                      <Text
                        variant="bodySm"
                        as="span"
                        tone={trend.isPositive ? "success" : "critical"}
                        fontWeight="medium"
                      >
                        {trend.isPositive ? '+' : ''}{trend.value}%
                      </Text>
                    </div>
                    <Text variant="bodySm" tone="subdued" as="span">
                      vs last period
                    </Text>
                  </InlineStack>
                </Box>
              )}
            </Box>
            <div className="stats-card-icon">
              <IconComponent />
            </div>
          </InlineStack>
        </div>
      </Box>
    </Card>
  );
}

// Stats Grid Component
interface StatsGridProps {
  stats: {
    totalCodes: number;
    activeCodes: number;
    totalUsages: number;
    recentUsages: any[];
  };
}

export function StatsGrid({ stats }: StatsGridProps) {
  const usageRate = stats.totalCodes > 0 ? (stats.totalUsages / stats.totalCodes) : 0;
  const activeRate = stats.totalCodes > 0 ? (stats.activeCodes / stats.totalCodes * 100) : 0;

  return (
    <div className="stats-grid">
      <StatsCard
        title="Total Invite Codes"
        value={stats.totalCodes}
        subtitle="All time"
        icon="ticket"
        variant="default"
      />
      
      <StatsCard
        title="Active Codes"
        value={stats.activeCodes}
        subtitle={`${activeRate.toFixed(1)}% of total`}
        icon="check"
        variant="success"
      />
      
      <StatsCard
        title="Total Uses"
        value={stats.totalUsages}
        subtitle="All time"
        icon="trending"
        variant="default"
      />
      
      <StatsCard
        title="Average Uses per Code"
        value={usageRate.toFixed(1)}
        subtitle="Efficiency metric"
        icon="clock"
        variant={usageRate > 2 ? "success" : usageRate > 1 ? "warning" : "critical"}
      />
    </div>
  );
}

// Styles to be added to CSS
export const statsCardStyles = `
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
  }
  
  .stats-card {
    position: relative;
  }
  
  .stats-card-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-medium);
    background-color: var(--color-bg-accent);
    color: var(--color-primary);
    opacity: 0.8;
  }
  
  .stats-card-success .stats-card-icon {
    background-color: rgba(79, 121, 66, 0.1);
    color: var(--color-success);
  }
  
  .stats-card-warning .stats-card-icon {
    background-color: rgba(184, 134, 11, 0.1);
    color: var(--color-warning);
  }
  
  .stats-card-critical .stats-card-icon {
    background-color: rgba(197, 85, 77, 0.1);
    color: var(--color-error);
  }
  
  .trend-indicator {
    padding: 2px 6px;
    border-radius: var(--radius-small);
    font-size: var(--font-size-xs);
    font-weight: 600;
  }
  
  .trend-positive {
    background-color: rgba(79, 121, 66, 0.1);
  }
  
  .trend-negative {
    background-color: rgba(197, 85, 77, 0.1);
  }
  
  @media (max-width: 768px) {
    .stats-grid {
      grid-template-columns: 1fr;
      gap: var(--space-md);
    }
  }
`;
