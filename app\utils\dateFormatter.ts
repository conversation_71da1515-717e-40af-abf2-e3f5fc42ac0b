/**
 * Global date formatting utilities for the Invite Code application
 * Provides consistent YYYY/MM/DD formatting across the entire application
 */

/**
 * Formats a Date object or date string to YYYY/MM/DD format
 * @param date - Date object, date string, or null/undefined
 * @returns Formatted date string in YYYY/MM/DD format or empty string if invalid
 */
export function formatDate(date: Date | string | null | undefined): string {
  if (!date) return "";
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return "";
    }
    
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    
    return `${year}/${month}/${day}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return "";
  }
}

/**
 * Formats a Date object or date string to YYYY/MM/DD HH:MM format
 * @param date - Date object, date string, or null/undefined
 * @returns Formatted datetime string in YYYY/MM/DD HH:MM format or empty string if invalid
 */
export function formatDateTime(date: Date | string | null | undefined): string {
  if (!date) return "";
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return "";
    }
    
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    
    return `${year}/${month}/${day} ${hours}:${minutes}`;
  } catch (error) {
    console.error('Error formatting datetime:', error);
    return "";
  }
}

/**
 * Formats a date for display in tables and lists
 * Shows "Never" for null/undefined dates, otherwise formats as YYYY/MM/DD
 * @param date - Date object, date string, or null/undefined
 * @returns Formatted date string or "Never"
 */
export function formatDateForDisplay(date: Date | string | null | undefined): string {
  if (!date) return "Never";
  return formatDate(date);
}

/**
 * Formats a date for form inputs (YYYY-MM-DD format for HTML date inputs)
 * @param date - Date object, date string, or null/undefined
 * @returns Formatted date string in YYYY-MM-DD format for HTML inputs
 */
export function formatDateForInput(date: Date | string | null | undefined): string {
  if (!date) return "";
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return "";
    }
    
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('Error formatting date for input:', error);
    return "";
  }
}

/**
 * Checks if a date is expired (past current date)
 * @param date - Date object, date string, or null/undefined
 * @returns true if date is in the past, false otherwise
 */
export function isDateExpired(date: Date | string | null | undefined): boolean {
  if (!date) return false;
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return false;
    }
    
    return dateObj < now;
  } catch (error) {
    console.error('Error checking if date is expired:', error);
    return false;
  }
}

/**
 * Gets relative time description (e.g., "2 days ago", "in 3 hours")
 * @param date - Date object, date string, or null/undefined
 * @returns Relative time string
 */
export function getRelativeTime(date: Date | string | null | undefined): string {
  if (!date) return "";
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return "";
    }
    
    const diffMs = dateObj.getTime() - now.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (Math.abs(diffDays) >= 1) {
      return diffDays > 0 ? `in ${diffDays} days` : `${Math.abs(diffDays)} days ago`;
    } else if (Math.abs(diffHours) >= 1) {
      return diffHours > 0 ? `in ${diffHours} hours` : `${Math.abs(diffHours)} hours ago`;
    } else if (Math.abs(diffMinutes) >= 1) {
      return diffMinutes > 0 ? `in ${diffMinutes} minutes` : `${Math.abs(diffMinutes)} minutes ago`;
    } else {
      return "just now";
    }
  } catch (error) {
    console.error('Error getting relative time:', error);
    return "";
  }
}
