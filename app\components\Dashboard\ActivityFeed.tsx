import { Card, Text, Box, InlineStack, Avatar, Badge } from "@shopify/polaris";
import { CodeBadge } from "../UI/CodeBadge";

interface ActivityItem {
  id: number;
  inviteCode: {
    code: string;
  };
  usedAt: string;
  customerId?: string;
  ipAddress?: string;
}

interface ActivityFeedProps {
  activities: ActivityItem[];
  title?: string;
}

export function ActivityFeed({ activities, title = "Recent Activity" }: ActivityFeedProps) {
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return "Just now";
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  };

  const getCustomerInitials = (customerId?: string) => {
    if (!customerId) return "?";
    return customerId.slice(0, 2).toUpperCase();
  };

  const getLocationFromIP = (ipAddress?: string) => {
    if (!ipAddress) return "Unknown location";
    
    // Simple IP-based location detection (in real app, use a service like ipapi.co)
    if (ipAddress.startsWith("192.168.") || ipAddress.startsWith("10.0.")) {
      return "Local network";
    }
    return "External";
  };

  if (activities.length === 0) {
    return (
      <Card>
        <Box padding="400">
          <Text variant="headingMd" as="h3">
            {title}
          </Text>
          <Box paddingBlockStart="400">
            <div className="activity-empty-state">
              <Text variant="bodyMd" tone="subdued" alignment="center">
                No recent activity to display
              </Text>
              <Text variant="bodySm" tone="subdued" alignment="center">
                Invite code usage will appear here
              </Text>
            </div>
          </Box>
        </Box>
      </Card>
    );
  }

  return (
    <Card>
      <Box padding="400">
        <InlineStack align="space-between" blockAlign="center">
          <Text variant="headingMd" as="h3">
            {title}
          </Text>
          <Badge tone="info">{activities.length} recent</Badge>
        </InlineStack>
        
        <Box paddingBlockStart="400">
          <div className="activity-feed">
            {activities.map((activity, index) => (
              <div key={activity.id} className="activity-item">
                <InlineStack gap="300" align="start">
                  <div className="activity-avatar">
                    <Avatar
                      size="small"
                      initials={getCustomerInitials(activity.customerId)}
                      name={activity.customerId || "Anonymous"}
                    />
                  </div>
                  
                  <div className="activity-content">
                    <InlineStack gap="200" align="start" wrap={false}>
                      <Text variant="bodyMd" as="p">
                        Code
                      </Text>
                      <CodeBadge 
                        code={activity.inviteCode.code} 
                        status="active" 
                        size="small"
                        copyable={false}
                      />
                      <Text variant="bodyMd" as="p">
                        was used
                      </Text>
                    </InlineStack>
                    
                    <Box paddingBlockStart="100">
                      <InlineStack gap="400" align="start">
                        <Text variant="bodySm" tone="subdued">
                          {formatTimeAgo(activity.usedAt)}
                        </Text>
                        {activity.customerId && (
                          <Text variant="bodySm" tone="subdued">
                            Customer: {activity.customerId}
                          </Text>
                        )}
                        <Text variant="bodySm" tone="subdued">
                          {getLocationFromIP(activity.ipAddress)}
                        </Text>
                      </InlineStack>
                    </Box>
                  </div>
                </InlineStack>
                
                {index < activities.length - 1 && (
                  <div className="activity-divider" />
                )}
              </div>
            ))}
          </div>
        </Box>
      </Box>
    </Card>
  );
}

// Quick Stats Component for Activity
interface QuickStatsProps {
  todayUsages: number;
  weekUsages: number;
  monthUsages: number;
}

export function QuickStats({ todayUsages, weekUsages, monthUsages }: QuickStatsProps) {
  return (
    <Card>
      <Box padding="400">
        <Text variant="headingMd" as="h3">
          Usage Summary
        </Text>
        
        <Box paddingBlockStart="400">
          <div className="quick-stats">
            <div className="quick-stat-item">
              <Text variant="heading2xl" as="p" fontWeight="bold">
                {todayUsages}
              </Text>
              <Text variant="bodySm" tone="subdued">
                Today
              </Text>
            </div>
            
            <div className="quick-stat-item">
              <Text variant="heading2xl" as="p" fontWeight="bold">
                {weekUsages}
              </Text>
              <Text variant="bodySm" tone="subdued">
                This Week
              </Text>
            </div>
            
            <div className="quick-stat-item">
              <Text variant="heading2xl" as="p" fontWeight="bold">
                {monthUsages}
              </Text>
              <Text variant="bodySm" tone="subdued">
                This Month
              </Text>
            </div>
          </div>
        </Box>
      </Box>
    </Card>
  );
}

// Styles to be added to CSS
export const activityFeedStyles = `
  .activity-feed {
    max-height: 400px;
    overflow-y: auto;
  }
  
  .activity-item {
    position: relative;
    padding: var(--space-md) 0;
  }
  
  .activity-item:first-child {
    padding-top: 0;
  }
  
  .activity-item:last-child {
    padding-bottom: 0;
  }
  
  .activity-avatar {
    flex-shrink: 0;
  }
  
  .activity-content {
    flex: 1;
    min-width: 0;
  }
  
  .activity-divider {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--color-border-light);
  }
  
  .activity-empty-state {
    text-align: center;
    padding: var(--space-xl) 0;
  }
  
  .quick-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-lg);
    text-align: center;
  }
  
  .quick-stat-item {
    padding: var(--space-md);
    border-radius: var(--radius-medium);
    background-color: var(--color-bg-accent);
  }
  
  @media (max-width: 768px) {
    .quick-stats {
      grid-template-columns: 1fr;
      gap: var(--space-md);
    }
    
    .activity-content .Polaris-InlineStack {
      flex-wrap: wrap;
    }
  }
`;
