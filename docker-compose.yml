# Supersleek Invite Code - Development Database Setup
# This docker-compose file sets up PostgreSQL and pgAdmin for local development
# For production, use managed database services (Supabase, Railway, etc.)

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: supertested-postgres-dev
    restart: unless-stopped
    environment:
      # Development database configuration
      POSTGRES_DB: ${DB_NAME:-supertested_invite_codes}
      POSTGRES_USER: ${DB_USER:-invite_app}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-dev_password_2024}
      POSTGRES_HOST_AUTH_METHOD: trust
      # Enable logging for development
      POSTGRES_INITDB_ARGS: "--auth-host=trust --auth-local=trust"
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./scripts/seed-dev-data.sql:/docker-entrypoint-initdb.d/02-seed.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-invite_app} -d ${DB_NAME:-supertested_invite_codes}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - supertested-network

  # pgAdmin - Database Management Interface
  pgadmin:
    image: dpage/pgadmin4:8.2
    container_name: supertested-pgadmin-dev
    restart: unless-stopped
    environment:
      # pgAdmin configuration
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-dev123456}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
      PGADMIN_LISTEN_PORT: 80
      # Disable authentication for development
      PGADMIN_CONFIG_AUTHENTICATION_SOURCES: "['internal']"
    ports:
      - "${PGADMIN_PORT:-8080}:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./scripts/pgadmin-servers.json:/pgadmin4/servers.json:ro
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - supertested-network

volumes:
  postgres_data:
    driver: local
    name: supertested_postgres_data
  pgadmin_data:
    driver: local
    name: supertested_pgadmin_data

networks:
  supertested-network:
    driver: bridge
    name: supertested-dev-network
