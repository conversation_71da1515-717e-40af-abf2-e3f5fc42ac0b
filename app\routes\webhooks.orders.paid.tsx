import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { incrementPurchasesCountByCode, getInviteCodeByCode } from "../models/invite-code.server";
import { tagCustomerWithInviteCode, applyMembershipTagToCustomer } from "../services/customer-tagging.server";

/**
 * Webhook handler for orders/paid
 * This webhook is triggered when an order is successfully paid
 * We use this to track successful purchases using invite codes
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  console.log("🔔 Received orders/paid webhook");

  try {
    const { topic, shop, session, admin, payload } = await authenticate.webhook(request);
    
    console.log(`📦 Processing order payment for shop: ${shop}`);
    console.log(`🎯 Topic: ${topic}`);

    if (topic !== "ORDERS_PAID") {
      console.log("❌ Unexpected webhook topic:", topic);
      return json({ success: false, error: "Unexpected webhook topic" }, { status: 400 });
    }

    // Extract order data from payload
    const order = payload;
    console.log(`💰 Order ID: ${order.id}, Total: ${order.total_price} ${order.currency}`);

    // Look for invite code in order attributes, note, or discount codes
    let inviteCode: string | null = null;

    // Check order attributes for invite code
    if (order.note_attributes && Array.isArray(order.note_attributes)) {
      const inviteCodeAttr = order.note_attributes.find(
        (attr: any) => attr.name === "invite_code" || attr.name === "inviteCode"
      );
      if (inviteCodeAttr) {
        inviteCode = inviteCodeAttr.value;
        console.log(`🎫 Found invite code in order attributes: ${inviteCode}`);
      }
    }

    // Check order note for invite code pattern
    if (!inviteCode && order.note) {
      const codeMatch = order.note.match(/invite[_\s]*code[:\s]*([A-Z0-9]+)/i);
      if (codeMatch) {
        inviteCode = codeMatch[1].toUpperCase();
        console.log(`🎫 Found invite code in order note: ${inviteCode}`);
      }
    }

    // Check discount codes (if invite codes are implemented as discount codes)
    if (!inviteCode && order.discount_codes && Array.isArray(order.discount_codes)) {
      for (const discount of order.discount_codes) {
        // Check if this discount code matches an invite code pattern
        if (discount.code && /^[A-Z0-9]{6,12}$/.test(discount.code)) {
          inviteCode = discount.code;
          console.log(`🎫 Found potential invite code in discount codes: ${inviteCode}`);
          break;
        }
      }
    }

    // Check line item properties for invite codes
    if (!inviteCode && order.line_items && Array.isArray(order.line_items)) {
      for (const lineItem of order.line_items) {
        if (lineItem.properties && Array.isArray(lineItem.properties)) {
          const inviteCodeProp = lineItem.properties.find(
            (prop: any) => prop.name === "invite_code" || prop.name === "inviteCode"
          );
          if (inviteCodeProp) {
            inviteCode = inviteCodeProp.value;
            console.log(`🎫 Found invite code in line item properties: ${inviteCode}`);
            break;
          }
        }
      }
    }

    if (!inviteCode) {
      console.log("ℹ️ No invite code found in order, skipping tracking");
      return json({ success: true, message: "No invite code found" });
    }

    // Increment the purchases count for this invite code
    console.log(`📈 Incrementing purchases count for invite code: ${inviteCode}`);
    const updatedCode = await incrementPurchasesCountByCode(inviteCode);

    if (updatedCode) {
      console.log(`✅ Successfully incremented purchases count for code ${inviteCode}`);
      console.log(`📊 New purchases count: ${updatedCode.purchasesCount}`);

      // Tag the customer with the invite code (bonus feature)
      if (order.customer && order.customer.id) {
        console.log(`🏷️ Tagging customer ${order.customer.id} with invite code ${inviteCode}`);

        try {
          const taggingResult = await tagCustomerWithInviteCode({
            customerId: order.customer.id.toString(),
            inviteCode,
            orderId: order.id.toString(),
            admin,
          });

          if (taggingResult.success) {
            console.log(`✅ Customer tagging successful: ${taggingResult.message}`);
          } else {
            console.log(`⚠️ Customer tagging failed: ${taggingResult.message}`);
          }
        } catch (taggingError: any) {
          console.error(`❌ Error tagging customer:`, taggingError);
          // Don't fail the webhook if tagging fails
        }

        // Also apply membership tag if the invite code has a membership
        try {
          const inviteCodeRecord = await getInviteCodeByCode(inviteCode);

          if (inviteCodeRecord && inviteCodeRecord.membershipId) {
            console.log(`🏷️ Applying membership tag for invite code ${inviteCode} to customer ${order.customer.id}`);

            const membershipResult = await applyMembershipTagToCustomer({
              customerId: order.customer.id.toString(),
              membershipId: inviteCodeRecord.membershipId,
              shop: session.shop,
              admin,
            });

            if (membershipResult.success) {
              console.log(`✅ Membership tag applied successfully: ${membershipResult.message}`);
            } else {
              console.log(`⚠️ Failed to apply membership tag: ${membershipResult.message}`);
            }
          } else {
            console.log(`ℹ️ No membership configured for invite code ${inviteCode}, skipping membership tagging`);
          }
        } catch (membershipError: any) {
          console.error(`❌ Error applying membership tag:`, membershipError);
          // Don't fail the webhook if membership tagging fails
        }
      } else {
        console.log(`ℹ️ No customer ID found in order, skipping customer tagging`);
      }

      return json({
        success: true,
        message: `Purchases count incremented for code ${inviteCode}`,
        code: inviteCode,
        newCount: updatedCode.purchasesCount,
      });
    } else {
      console.log(`⚠️ Invite code ${inviteCode} not found or could not be updated`);
      return json({
        success: false,
        message: `Invite code ${inviteCode} not found`,
        code: inviteCode,
      });
    }

  } catch (error: any) {
    console.error("❌ Error processing orders/paid webhook:", error);
    return json(
      { 
        success: false, 
        error: "Internal server error",
        details: error.message,
      },
      { status: 500 }
    );
  }
};

/**
 * Helper function to extract invite code from various order fields
 * This can be customized based on how invite codes are stored in orders
 */
function extractInviteCodeFromOrder(order: any): string | null {
  // Priority order for checking invite codes:
  // 1. Order attributes
  // 2. Order note
  // 3. Discount codes
  // 4. Line item properties
  // 5. Customer tags (if available)

  // Check order attributes
  if (order.note_attributes && Array.isArray(order.note_attributes)) {
    const inviteCodeAttr = order.note_attributes.find(
      (attr: any) => 
        attr.name?.toLowerCase().includes("invite") && 
        attr.name?.toLowerCase().includes("code")
    );
    if (inviteCodeAttr?.value) {
      return inviteCodeAttr.value.toString().toUpperCase();
    }
  }

  // Check order note with various patterns
  if (order.note) {
    const patterns = [
      /invite[_\s]*code[:\s]*([A-Z0-9]+)/i,
      /code[:\s]*([A-Z0-9]{6,12})/i,
      /referral[:\s]*([A-Z0-9]+)/i,
    ];

    for (const pattern of patterns) {
      const match = order.note.match(pattern);
      if (match) {
        return match[1].toUpperCase();
      }
    }
  }

  // Check discount codes
  if (order.discount_codes && Array.isArray(order.discount_codes)) {
    for (const discount of order.discount_codes) {
      if (discount.code && /^[A-Z0-9]{6,12}$/.test(discount.code)) {
        return discount.code.toUpperCase();
      }
    }
  }

  // Check line item properties
  if (order.line_items && Array.isArray(order.line_items)) {
    for (const lineItem of order.line_items) {
      if (lineItem.properties && Array.isArray(lineItem.properties)) {
        const inviteCodeProp = lineItem.properties.find(
          (prop: any) => 
            prop.name?.toLowerCase().includes("invite") && 
            prop.name?.toLowerCase().includes("code")
        );
        if (inviteCodeProp?.value) {
          return inviteCodeProp.value.toString().toUpperCase();
        }
      }
    }
  }

  return null;
}

// Export for potential use in other webhook handlers
export { extractInviteCodeFromOrder };
