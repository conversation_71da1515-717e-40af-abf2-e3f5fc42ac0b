-- CreateTable
CREATE TABLE "membership_tags" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "customer_tag" VARCHAR(255) NOT NULL,
    "order_tag" VARCHAR(255),
    "shop" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL,

    CONSTRAINT "membership_tags_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "membership_tags_shop_idx" ON "membership_tags"("shop");

-- CreateIndex
CREATE UNIQUE INDEX "membership_tags_shop_customer_tag_key" ON "membership_tags"("shop", "customer_tag");
