export interface PaginationParams {
  page: number;
  limit: number;
  total: number;
}

export interface PaginationResult {
  page: number;
  limit: number;
  total: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
  skip: number;
}

export function calculatePagination(params: PaginationParams): PaginationResult {
  const { page, limit, total } = params;
  const pages = Math.ceil(total / limit);
  const skip = (page - 1) * limit;

  return {
    page,
    limit,
    total,
    pages,
    hasNext: page < pages,
    hasPrev: page > 1,
    skip,
  };
}

export function parsePaginationParams(
  searchParams: URLSearchParams,
  defaultLimit: number = 20
): { page: number; limit: number } {
  const page = Math.max(1, parseInt(searchParams.get("page") || "1", 10));
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get("limit") || defaultLimit.toString(), 10)));

  return { page, limit };
}

// Force reload
