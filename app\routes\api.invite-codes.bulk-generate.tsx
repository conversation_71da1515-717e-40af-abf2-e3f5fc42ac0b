import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { createInviteCode, generateUniqueInviteCode } from "../models/invite-code.server";
import { getMembershipTagById } from "../models/membership-tag.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  console.log("🔄 Processing bulk invite code generation...");

  try {
    const data = await request.json();
    console.log("📝 Bulk generation request:", data);

    const {
      numberOfCodes,
      maxUsesPerCode,
      expiresAt,
      membershipId,
      partnerId,
      createdBy,
      shopId,
    } = data;

    // Validation
    if (!numberOfCodes || numberOfCodes < 1 || numberOfCodes > 1000) {
      return json(
        { error: "Number of codes must be between 1 and 1000" },
        { status: 400 }
      );
    }

    if (!maxUsesPerCode || maxUsesPerCode < 1) {
      return json(
        { error: "Maximum uses per code must be at least 1" },
        { status: 400 }
      );
    }

    if (!membershipId) {
      return json(
        { error: "Membership plan is required" },
        { status: 400 }
      );
    }

    if (!createdBy || !shopId) {
      return json(
        { error: "Missing required authentication data" },
        { status: 400 }
      );
    }

    // Validate membership tag exists
    console.log(`🔍 About to call getMembershipTagById with id: ${parseInt(membershipId)}, shop: ${shopId}`);
    const membershipTag = await getMembershipTagById(parseInt(membershipId), shopId);
    if (!membershipTag) {
      return json(
        { error: "Invalid membership plan selected" },
        { status: 400 }
      );
    }

    // Validate expiry date if provided
    let expiryDate: Date | undefined;
    if (expiresAt) {
      expiryDate = new Date(expiresAt);
      if (expiryDate <= new Date()) {
        return json(
          { error: "Expiry date must be in the future" },
          { status: 400 }
        );
      }
    }

    console.log(`🔄 Generating ${numberOfCodes} invite codes...`);

    // Generate codes in batches to avoid overwhelming the database
    const batchSize = 50;
    const codes = [];
    const errors = [];

    for (let i = 0; i < numberOfCodes; i += batchSize) {
      const batchEnd = Math.min(i + batchSize, numberOfCodes);
      const batchPromises = [];

      for (let j = i; j < batchEnd; j++) {
        const codePromise = createInviteCode({
          maxUses: maxUsesPerCode,
          expiresAt: expiryDate,
          createdBy,
          shopId,
          partnerId,
          membershipId,
        }).catch((error) => {
          console.error(`❌ Error creating code ${j + 1}:`, error);
          errors.push(`Code ${j + 1}: ${error.message}`);
          return null;
        });

        batchPromises.push(codePromise);
      }

      const batchResults = await Promise.all(batchPromises);
      const successfulCodes = batchResults.filter(code => code !== null);
      codes.push(...successfulCodes);

      console.log(`✅ Batch ${Math.floor(i / batchSize) + 1}: Generated ${successfulCodes.length}/${batchEnd - i} codes`);
    }

    console.log(`✅ Bulk generation complete: ${codes.length}/${numberOfCodes} codes generated`);

    if (codes.length === 0) {
      return json(
        { 
          error: "Failed to generate any codes. Please try again.",
          details: errors.slice(0, 5), // Return first 5 errors for debugging
        },
        { status: 500 }
      );
    }

    // Return success with generated codes
    const response = {
      success: true,
      codes: codes.map(code => ({
        id: code.id,
        code: code.code,
        maxUses: code.maxUses,
        expiresAt: code.expiresAt,
        membershipId: code.membershipId,
        partnerId: code.partnerId,
        createdAt: code.createdAt,
      })),
      generated: codes.length,
      requested: numberOfCodes,
      errors: errors.length > 0 ? errors.slice(0, 5) : undefined,
    };

    if (errors.length > 0) {
      console.warn(`⚠️ ${errors.length} errors occurred during generation`);
    }

    return json(response);

  } catch (error: any) {
    console.error("❌ Error in bulk generation API:", error);
    return json(
      { 
        error: "Internal server error during bulk generation",
        details: error.message,
      },
      { status: 500 }
    );
  }
};

// Alternative implementation using database transactions for better performance
export async function bulkGenerateWithTransaction(data: {
  numberOfCodes: number;
  maxUsesPerCode: number;
  expiresAt?: Date;
  membershipId: string;
  partnerId?: number;
  createdBy: string;
  shopId: string;
}) {
  console.log("🔄 Bulk generating with transaction...");

  const {
    numberOfCodes,
    maxUsesPerCode,
    expiresAt,
    membershipId,
    partnerId,
    createdBy,
    shopId,
  } = data;

  try {
    // Generate unique codes first
    const codes = [];
    const codeSet = new Set();

    while (codes.length < numberOfCodes) {
      const code = await generateUniqueInviteCode();
      if (!codeSet.has(code)) {
        codeSet.add(code);
        codes.push({
          code,
          maxUses: maxUsesPerCode,
          expiresAt,
          createdBy,
          shopId,
          partnerId,
          membershipId,
        });
      }
    }

    console.log(`✅ Generated ${codes.length} unique codes`);

    // TODO: Implement bulk insert with Prisma
    // This would be more efficient than individual creates
    // const result = await db.inviteCode.createMany({
    //   data: codes,
    //   skipDuplicates: true,
    // });

    return {
      success: true,
      codes,
      generated: codes.length,
    };

  } catch (error) {
    console.error("❌ Error in bulk generation with transaction:", error);
    throw error;
  }
}

// Export for potential use in other parts of the application
export { action as POST };
