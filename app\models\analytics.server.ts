import { PrismaClient } from "@prisma/client";
import type { InviteCode, Usage, Partner } from "@prisma/client";

const db = new PrismaClient();

export interface PartnerConversionData {
  partnerId: number | null;
  partnerName: string;
  totalCodes: number;
  totalApplications: number;
  totalPurchases: number;
  conversionRate: number;
  revenue: number; // This would need to be calculated from order data
  averageOrderValue: number;
  recentActivity: Date | null;
}

export interface InviteCodePerformance {
  id: number;
  code: string;
  partnerId: number | null;
  partnerName: string | null;
  membershipId: string;
  applications: number;
  purchases: number;
  conversionRate: number;
  createdAt: Date;
  lastUsed: Date | null;
  isActive: boolean;
}

export interface ConversionMetrics {
  totalInviteCodes: number;
  activeInviteCodes: number;
  totalApplications: number;
  totalPurchases: number;
  overallConversionRate: number;
  topPerformingCodes: InviteCodePerformance[];
  partnerPerformance: PartnerConversionData[];
  recentConversions: Array<{
    code: string;
    partnerName: string | null;
    usedAt: Date;
    converted: boolean;
    customerId: string | null;
  }>;
}

export interface AnalyticsFilters {
  shopId: string;
  dateFrom?: Date;
  dateTo?: Date;
  partnerId?: number;
  membershipId?: string;
  limit?: number;
}

// Get comprehensive analytics data for the dashboard
export async function getAnalyticsData(filters: AnalyticsFilters): Promise<ConversionMetrics> {
  const { shopId, dateFrom, dateTo, partnerId, membershipId, limit = 10 } = filters;

  // Build date filter for queries
  const dateFilter = dateFrom && dateTo ? {
    createdAt: {
      gte: dateFrom,
      lte: dateTo,
    },
  } : {};

  // Build partner filter
  const partnerFilter = partnerId ? { partnerId } : {};
  
  // Build membership filter
  const membershipFilter = membershipId ? { membershipId } : {};

  // Get basic metrics
  const [totalInviteCodes, activeInviteCodes, totalApplicationsSum, totalPurchasesSum] = await Promise.all([
    db.inviteCode.count({
      where: {
        shopId,
        ...partnerFilter,
        ...membershipFilter,
        ...dateFilter,
      },
    }),
    db.inviteCode.count({
      where: {
        shopId,
        isActive: true,
        ...partnerFilter,
        ...membershipFilter,
        ...dateFilter,
      },
    }),
    db.inviteCode.aggregate({
      where: {
        shopId,
        ...partnerFilter,
        ...membershipFilter,
        ...dateFilter,
      },
      _sum: {
        applicationsCount: true,
      },
    }),
    db.inviteCode.aggregate({
      where: {
        shopId,
        ...partnerFilter,
        ...membershipFilter,
        ...dateFilter,
      },
      _sum: {
        purchasesCount: true,
      },
    }),
  ]);

  const totalApplications = totalApplicationsSum._sum.applicationsCount || 0;
  const totalPurchases = totalPurchasesSum._sum.purchasesCount || 0;
  const overallConversionRate = totalApplications > 0 ? (totalPurchases / totalApplications) * 100 : 0;

  // Get top performing codes
  const topPerformingCodes = await getTopPerformingCodes(shopId, limit, dateFilter);

  // Get partner performance data
  const partnerPerformance = await getPartnerPerformance(shopId, dateFilter);

  // Get recent conversions
  const recentConversions = await getRecentConversions(shopId, limit, dateFilter);

  return {
    totalInviteCodes,
    activeInviteCodes,
    totalApplications,
    totalPurchases,
    overallConversionRate,
    topPerformingCodes,
    partnerPerformance,
    recentConversions,
  };
}

// Get top performing invite codes
async function getTopPerformingCodes(
  shopId: string, 
  limit: number, 
  dateFilter: any
): Promise<InviteCodePerformance[]> {
  const codes = await db.inviteCode.findMany({
    where: {
      shopId,
      ...dateFilter,
    },
    include: {
      partner: {
        select: {
          name: true,
        },
      },
      usages: {
        select: {
          usedAt: true,
        },
        orderBy: {
          usedAt: 'desc',
        },
        take: 1,
      },
    },
    orderBy: [
      { purchasesCount: 'desc' },
      { applicationsCount: 'desc' },
    ],
    take: limit,
  });

  return codes.map(code => ({
    id: code.id,
    code: code.code,
    partnerId: code.partnerId,
    partnerName: code.partner?.name || null,
    membershipId: code.membershipId,
    applications: code.applicationsCount,
    purchases: code.purchasesCount,
    conversionRate: code.applicationsCount > 0 ? (code.purchasesCount / code.applicationsCount) * 100 : 0,
    createdAt: code.createdAt,
    lastUsed: code.usages && code.usages.length > 0 ? code.usages[0].usedAt : null,
    isActive: code.isActive,
  }));
}

// Get partner performance data
async function getPartnerPerformance(shopId: string, dateFilter: any): Promise<PartnerConversionData[]> {
  // Get partners with their invite code performance
  const partnersWithCodes = await db.partner.findMany({
    where: {
      shopId,
      isActive: true,
    },
    include: {
      inviteCodes: {
        where: dateFilter,
        select: {
          applicationsCount: true,
          purchasesCount: true,
          createdAt: true,
          usages: {
            select: {
              usedAt: true,
            },
            orderBy: {
              usedAt: 'desc',
            },
            take: 1,
          },
        },
      },
    },
  });

  // Also get codes without partners
  const codesWithoutPartner = await db.inviteCode.findMany({
    where: {
      shopId,
      partnerId: null,
      ...dateFilter,
    },
    select: {
      applicationsCount: true,
      purchasesCount: true,
      createdAt: true,
      usages: {
        select: {
          usedAt: true,
        },
        orderBy: {
          usedAt: 'desc',
        },
        take: 1,
      },
    },
  });

  const partnerData: PartnerConversionData[] = partnersWithCodes.map(partner => {
    const totalCodes = partner.inviteCodes.length;
    const totalApplications = partner.inviteCodes.reduce((sum, code) => sum + code.applicationsCount, 0);
    const totalPurchases = partner.inviteCodes.reduce((sum, code) => sum + code.purchasesCount, 0);
    const conversionRate = totalApplications > 0 ? (totalPurchases / totalApplications) * 100 : 0;
    
    // Get most recent activity
    const allUsages = partner.inviteCodes.flatMap(code => code.usages);
    const recentActivity = allUsages.length > 0
      ? allUsages.sort((a, b) => new Date(b.usedAt).getTime() - new Date(a.usedAt).getTime())[0]?.usedAt || null
      : null;

    return {
      partnerId: partner.id,
      partnerName: partner.name,
      totalCodes,
      totalApplications,
      totalPurchases,
      conversionRate,
      revenue: 0, // Would need order data integration
      averageOrderValue: 0, // Would need order data integration
      recentActivity,
    };
  });

  // Add unassigned codes data
  if (codesWithoutPartner.length > 0) {
    const totalApplications = codesWithoutPartner.reduce((sum, code) => sum + code.applicationsCount, 0);
    const totalPurchases = codesWithoutPartner.reduce((sum, code) => sum + code.purchasesCount, 0);
    const conversionRate = totalApplications > 0 ? (totalPurchases / totalApplications) * 100 : 0;
    
    const allUnassignedUsages = codesWithoutPartner.flatMap(code => code.usages);
    const recentActivity = allUnassignedUsages.length > 0
      ? allUnassignedUsages.sort((a, b) => new Date(b.usedAt).getTime() - new Date(a.usedAt).getTime())[0]?.usedAt || null
      : null;

    partnerData.push({
      partnerId: null,
      partnerName: "Unassigned Codes",
      totalCodes: codesWithoutPartner.length,
      totalApplications,
      totalPurchases,
      conversionRate,
      revenue: 0,
      averageOrderValue: 0,
      recentActivity,
    });
  }

  return partnerData.sort((a, b) => b.conversionRate - a.conversionRate);
}

// Get recent conversions (applications and purchases)
async function getRecentConversions(
  shopId: string, 
  limit: number, 
  dateFilter: any
): Promise<Array<{
  code: string;
  partnerName: string | null;
  usedAt: Date;
  converted: boolean;
  customerId: string | null;
}>> {
  const recentUsages = await db.usage.findMany({
    where: {
      inviteCode: {
        shopId,
        ...dateFilter,
      },
    },
    include: {
      inviteCode: {
        include: {
          partner: {
            select: {
              name: true,
            },
          },
        },
      },
    },
    orderBy: {
      usedAt: 'desc',
    },
    take: limit,
  });

  return recentUsages.map(usage => ({
    code: usage.inviteCode.code,
    partnerName: usage.inviteCode.partner?.name || null,
    usedAt: usage.usedAt,
    converted: usage.inviteCode.purchasesCount > 0, // Simple conversion check
    customerId: usage.customerId,
  }));
}

// Get analytics data for a specific date range
export async function getAnalyticsForDateRange(
  shopId: string,
  startDate: Date,
  endDate: Date
): Promise<ConversionMetrics> {
  return getAnalyticsData({
    shopId,
    dateFrom: startDate,
    dateTo: endDate,
  });
}

// Get analytics data for a specific partner
export async function getPartnerAnalytics(
  shopId: string,
  partnerId: number
): Promise<ConversionMetrics> {
  return getAnalyticsData({
    shopId,
    partnerId,
  });
}
