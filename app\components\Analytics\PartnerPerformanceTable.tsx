import {
  Card,
  Text,
  Box,
  DataTable,
  Badge,
  InlineStack,
  BlockStack,
  EmptyState,
} from "@shopify/polaris";
import type { PartnerConversionData } from "../../models/analytics.server";

interface PartnerPerformanceTableProps {
  partnerPerformance: PartnerConversionData[];
}

export function PartnerPerformanceTable({ partnerPerformance }: PartnerPerformanceTableProps) {
  if (partnerPerformance.length === 0) {
    return (
      <Card>
        <EmptyState
          heading="No partner data available"
          image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
        >
          <p>Create invite codes and assign them to partners to see performance data.</p>
        </EmptyState>
      </Card>
    );
  }

  const formatDate = (date: Date | null) => {
    if (!date) return "Never";
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  const getConversionBadge = (rate: number) => {
    if (rate >= 20) return <Badge tone="success">Excellent</Badge>;
    if (rate >= 10) return <Badge tone="attention">Good</Badge>;
    if (rate >= 5) return <Badge tone="warning">Fair</Badge>;
    return <Badge tone="critical">Poor</Badge>;
  };

  const rows = partnerPerformance.map((partner) => [
    <Box key={`name-${partner.partnerId}`}>
      <BlockStack gap="100">
        <Text variant="bodyMd" fontWeight="medium">
          {partner.partnerName}
        </Text>
        {partner.partnerId === null && (
          <Badge tone="info">Unassigned</Badge>
        )}
      </BlockStack>
    </Box>,
    
    <Text key={`codes-${partner.partnerId}`} variant="bodyMd">
      {partner.totalCodes}
    </Text>,
    
    <Text key={`applications-${partner.partnerId}`} variant="bodyMd">
      {partner.totalApplications.toLocaleString()}
    </Text>,
    
    <Text key={`purchases-${partner.partnerId}`} variant="bodyMd" fontWeight="medium">
      {partner.totalPurchases.toLocaleString()}
    </Text>,
    
    <Box key={`conversion-${partner.partnerId}`}>
      <InlineStack gap="200" align="start">
        <Text variant="bodyMd" fontWeight="medium">
          {partner.conversionRate.toFixed(1)}%
        </Text>
        {getConversionBadge(partner.conversionRate)}
      </InlineStack>
    </Box>,
    
    <Text key={`activity-${partner.partnerId}`} variant="bodySm" tone="subdued">
      {formatDate(partner.recentActivity)}
    </Text>,
  ]);

  const headings = [
    "Partner",
    "Codes",
    "Applications",
    "Purchases",
    "Conversion Rate",
    "Last Activity",
  ];

  return (
    <Card>
      <Box padding="400">
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text variant="headingMd" as="h3">
              Partner Performance
            </Text>
            <Badge tone="info">
              {partnerPerformance.length} {partnerPerformance.length === 1 ? "Partner" : "Partners"}
            </Badge>
          </InlineStack>
          
          <DataTable
            columnContentTypes={[
              "text",
              "numeric",
              "numeric", 
              "numeric",
              "text",
              "text",
            ]}
            headings={headings}
            rows={rows}
            footerContent={
              partnerPerformance.length > 0 ? (
                <Text variant="bodySm" tone="subdued">
                  Showing {partnerPerformance.length} partner{partnerPerformance.length !== 1 ? "s" : ""} 
                  sorted by conversion rate
                </Text>
              ) : undefined
            }
          />
        </BlockStack>
      </Box>
    </Card>
  );
}
