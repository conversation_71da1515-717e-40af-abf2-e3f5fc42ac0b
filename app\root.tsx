import {
  Links,
  Meta,
  <PERSON>let,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  type LinksFunction,
} from "@remix-run/react";

export const links: LinksFunction = () => [];

export default function App() {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <meta name="color-scheme" content="light dark" />
        <link rel="preconnect" href="https://cdn.shopify.com/" />
        <link
          rel="stylesheet"
          href="https://cdn.shopify.com/static/fonts/inter/v4/styles.css"
        />
        <Meta />
        <Links />
        <style
          dangerouslySetInnerHTML={{
            __html: `
              /* Enhanced Dark Mode Styles */
              [data-theme="dark"] .Polaris-Text--root,
              [data-theme="dark"] .Polaris-DisplayText--root,
              [data-theme="dark"] .Polaris-Heading--root {
                color: #e1e3e5 !important;
              }

              [data-theme="dark"] .Polaris-Text--subdued {
                color: #b5b5b5 !important;
              }

              [data-theme="dark"] .Polaris-Card {
                background: #2a2a2a !important;
                border-color: #404040 !important;
                color: #e1e3e5 !important;
              }

              [data-theme="dark"] .Polaris-DataTable__Cell {
                border-color: #404040 !important;
                color: #e1e3e5 !important;
                background: #2a2a2a !important;
              }

              [data-theme="dark"] .Polaris-DataTable__Cell--header {
                background: #333333 !important;
                color: #e1e3e5 !important;
              }

              [data-theme="dark"] .Polaris-TextField__Input,
              [data-theme="dark"] .Polaris-Select__Input {
                background: #2a2a2a !important;
                border-color: #404040 !important;
                color: #e1e3e5 !important;
              }

              [data-theme="dark"] .Polaris-Badge--statusSuccess {
                background: #00a47c !important;
                color: white !important;
              }

              [data-theme="dark"] .Polaris-Badge--statusCritical {
                background: #ff6b47 !important;
                color: white !important;
              }

              .theme-toggle-button {
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                z-index: 1000 !important;
                background: var(--p-color-bg-surface, #ffffff) !important;
                border: 1px solid var(--p-color-border, #e1e3e5) !important;
                border-radius: 50% !important;
                width: 48px !important;
                height: 48px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                transition: all 0.2s ease !important;
                cursor: pointer !important;
              }

              .theme-toggle-button:hover {
                transform: scale(1.05) !important;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
              }

              .fade-in {
                animation: fadeIn 0.3s ease-out;
              }

              @keyframes fadeIn {
                from {
                  opacity: 0;
                  transform: translateY(10px);
                }
                to {
                  opacity: 1;
                  transform: translateY(0);
                }
              }

              .hover-lift {
                transition: transform 0.2s ease, box-shadow 0.2s ease;
              }

              .hover-lift:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
              }
            `,
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Theme initialization script to prevent flash
              (function() {
                const theme = localStorage.getItem('theme') ||
                  (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                document.documentElement.setAttribute('data-theme', theme);

                // Apply Polaris theme classes and enhanced styling
                if (theme === 'dark') {
                  document.documentElement.classList.add('Polaris-Dark');
                  document.documentElement.classList.remove('Polaris-Light');
                  document.body.style.backgroundColor = '#1a1a1a';
                  document.body.style.color = '#e1e3e5';

                  // Enhanced dark mode CSS variables
                  document.documentElement.style.setProperty('--p-color-bg', '#1a1a1a');
                  document.documentElement.style.setProperty('--p-color-bg-surface', '#2a2a2a');
                  document.documentElement.style.setProperty('--p-color-bg-surface-secondary', '#333333');
                  document.documentElement.style.setProperty('--p-color-text', '#e1e3e5');
                  document.documentElement.style.setProperty('--p-color-text-secondary', '#b5b5b5');
                  document.documentElement.style.setProperty('--p-color-border', '#404040');
                  document.documentElement.style.setProperty('--p-color-border-secondary', '#333333');
                } else {
                  document.documentElement.classList.add('Polaris-Light');
                  document.documentElement.classList.remove('Polaris-Dark');
                  document.body.style.backgroundColor = '#ffffff';
                  document.body.style.color = '#202223';
                }
              })();
            `,
          }}
        />
      </head>
      <body>
        <a href="#main-content" className="skip-link" style={{
          position: 'absolute',
          left: '-9999px',
          width: '1px',
          height: '1px',
          overflow: 'hidden'
        }}>
          Skip to main content
        </a>
        <Outlet />
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}
