import prisma from "../db.server";

export interface MembershipTag {
  id: number;
  name: string;
  customerTag: string;
  orderTag: string | null;
  shop: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateMembershipTagData {
  name: string;
  customerTag: string;
  orderTag: string | null;
}

/**
 * Get all membership tags for a shop
 */
export async function getMembershipTags(shop: string): Promise<MembershipTag[]> {
  console.log(`📋 Getting membership tags for shop: ${shop}`);
  
  try {
    const tags = await prisma.membershipTag.findMany({
      where: { shop },
      orderBy: { createdAt: "desc" },
    });

    console.log(`✅ Found ${tags.length} membership tags`);
    return tags;
  } catch (error) {
    console.error("❌ Error fetching membership tags:", error);
    throw new Error("Failed to fetch membership tags");
  }
}

/**
 * Get a single membership tag by ID
 */
export async function getMembershipTagById(id: number, shop: string): Promise<MembershipTag | null> {
  console.log(`🔍 Getting membership tag by ID: ${id} for shop: ${shop}`);

  try {
    const tag = await prisma.membershipTag.findFirst({
      where: { id, shop },
    });

    if (tag) {
      console.log(`✅ Found membership tag: ${tag.name} (ID: ${tag.id})`);
    } else {
      console.log(`❌ Membership tag not found: ID ${id}`);
    }

    return tag;
  } catch (error) {
    console.error("❌ Error getting membership tag by ID:", error);
    return null;
  }
}

/**
 * Get membership tags formatted for dropdown
 */
export async function getMembershipTagsForDropdown(shop: string) {
  console.log(`📋 Getting membership tags for dropdown`);

  try {
    const tags = await getMembershipTags(shop);
    const options = tags.map(tag => ({
      value: tag.id.toString(),
      label: tag.name,
    }));

    console.log(`✅ Prepared ${options.length} membership tag options for dropdown`);
    return options;
  } catch (error) {
    console.error("❌ Error preparing membership tags for dropdown:", error);
    return [];
  }
}

/**
 * Create a new membership tag
 */
export async function createMembershipTag(
  shop: string,
  data: CreateMembershipTagData
): Promise<MembershipTag> {
  console.log(`➕ Creating membership tag: ${data.name} for shop: ${shop}`);
  
  try {
    // Check if customer tag already exists
    const existingTag = await prisma.membershipTag.findFirst({
      where: {
        shop,
        customerTag: data.customerTag,
      },
    });

    if (existingTag) {
      throw new Error(`Customer tag "${data.customerTag}" already exists`);
    }

    const tag = await prisma.membershipTag.create({
      data: {
        ...data,
        shop,
      },
    });

    console.log(`✅ Created membership tag: ${tag.name} (ID: ${tag.id})`);
    return tag;
  } catch (error) {
    console.error("❌ Error creating membership tag:", error);
    throw error;
  }
}

/**
 * Update a membership tag
 */
export async function updateMembershipTag(
  shop: string,
  id: number,
  data: Partial<CreateMembershipTagData>
): Promise<MembershipTag> {
  console.log(`📝 Updating membership tag ${id} for shop: ${shop}`);
  
  try {
    // Check if the tag exists and belongs to the shop
    const existingTag = await prisma.membershipTag.findFirst({
      where: { id, shop },
    });

    if (!existingTag) {
      throw new Error("Membership tag not found");
    }

    // If updating customer tag, check for duplicates
    if (data.customerTag && data.customerTag !== existingTag.customerTag) {
      const duplicateTag = await prisma.membershipTag.findFirst({
        where: {
          shop,
          customerTag: data.customerTag,
          id: { not: id },
        },
      });

      if (duplicateTag) {
        throw new Error(`Customer tag "${data.customerTag}" already exists`);
      }
    }

    const tag = await prisma.membershipTag.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });

    console.log(`✅ Updated membership tag: ${tag.name} (ID: ${tag.id})`);
    return tag;
  } catch (error) {
    console.error("❌ Error updating membership tag:", error);
    throw error;
  }
}

/**
 * Delete a membership tag
 */
export async function deleteMembershipTag(shop: string, id: number): Promise<void> {
  console.log(`🗑️ Deleting membership tag ${id} for shop: ${shop}`);
  
  try {
    // Check if the tag exists and belongs to the shop
    const existingTag = await prisma.membershipTag.findFirst({
      where: { id, shop },
    });

    if (!existingTag) {
      throw new Error("Membership tag not found");
    }

    // Check if any invite codes are using this membership tag
    const codesUsingTag = await prisma.inviteCode.count({
      where: {
        shop,
        membershipId: id.toString(),
      },
    });

    if (codesUsingTag > 0) {
      throw new Error(`Cannot delete membership tag. ${codesUsingTag} invite codes are using this tag.`);
    }

    await prisma.membershipTag.delete({
      where: { id },
    });

    console.log(`✅ Deleted membership tag: ${existingTag.name} (ID: ${id})`);
  } catch (error) {
    console.error("❌ Error deleting membership tag:", error);
    throw error;
  }
}


