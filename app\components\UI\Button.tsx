import { Button as PolarisButton, type <PERSON>ton<PERSON>rops } from "@shopify/polaris";
import { forwardRef } from "react";

interface CustomButtonProps extends ButtonProps {
  variant?: "primary" | "secondary" | "tertiary" | "success" | "warning" | "critical";
  size?: "micro" | "slim" | "medium" | "large";
  fullWidth?: boolean;
  loading?: boolean;
  className?: string;
}

export const Button = forwardRef<HTMLButtonElement, CustomButtonProps>(
  ({ variant = "primary", className = "", ...props }, ref) => {
    // Map custom variants to Polaris variants
    const getPolarisVariant = (variant: string) => {
      switch (variant) {
        case "primary":
          return "primary";
        case "secondary":
          return "secondary";
        case "tertiary":
          return "tertiary";
        case "success":
          return "primary";
        case "warning":
          return "primary";
        case "critical":
          return "critical";
        default:
          return "primary";
      }
    };

    // Add custom classes for variants
    const getCustomClasses = (variant: string) => {
      const baseClasses = "transition-all focus-ring";
      
      switch (variant) {
        case "success":
          return `${baseClasses} button-success`;
        case "warning":
          return `${baseClasses} button-warning`;
        default:
          return baseClasses;
      }
    };

    const combinedClassName = `${getCustomClasses(variant)} ${className}`.trim();

    return (
      <PolarisButton
        ref={ref}
        variant={getPolarisVariant(variant)}
        className={combinedClassName}
        {...props}
      />
    );
  }
);

Button.displayName = "Button";

// Custom button styles (to be added to CSS)
export const buttonStyles = `
  .button-success {
    background-color: var(--color-success) !important;
    border-color: var(--color-success) !important;
    color: white !important;
  }
  
  .button-success:hover {
    background-color: var(--color-success) !important;
    filter: brightness(1.1);
  }
  
  .button-warning {
    background-color: var(--color-warning) !important;
    border-color: var(--color-warning) !important;
    color: white !important;
  }
  
  .button-warning:hover {
    background-color: var(--color-warning) !important;
    filter: brightness(1.1);
  }
  
  .focus-ring:focus {
    outline: 2px solid var(--color-primary) !important;
    outline-offset: 2px !important;
  }
`;
