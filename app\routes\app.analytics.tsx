import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useNavigate, useSearchParams } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  InlineStack,
  BlockStack,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import { getAnalyticsData } from "../models/analytics.server";
import { ConversionMetrics } from "../components/Analytics/ConversionMetrics";
import { PartnerPerformanceTable } from "../components/Analytics/PartnerPerformanceTable";
import { TopPerformingCodes } from "../components/Analytics/TopPerformingCodes";
import { RecentActivity } from "../components/Analytics/RecentActivity";
import { AnalyticsFilters } from "../components/Analytics/AnalyticsFilters";
import { ThemeToggle } from "../components/UI/ThemeToggle";
import { useState, useCallback } from "react";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  console.log("📊 Loading analytics dashboard...");
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  const url = new URL(request.url);
  const searchParams = url.searchParams;

  // Parse filters from URL parameters with validation
  const parseDate = (dateString: string | null) => {
    if (!dateString) return undefined;
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? undefined : date;
    } catch {
      return undefined;
    }
  };

  const filters = {
    shopId: shop,
    partnerId: searchParams.get("partnerId") ? parseInt(searchParams.get("partnerId")!) : undefined,
    membershipId: searchParams.get("membershipId") || undefined,
    dateFrom: parseDate(searchParams.get("dateFrom")),
    dateTo: parseDate(searchParams.get("dateTo")),
    limit: 10,
  };

  console.log(`📈 Getting analytics data for shop: ${shop}`, filters);

  try {
    const analyticsData = await getAnalyticsData(filters);
    console.log("📊 Analytics data loaded:", {
      totalCodes: analyticsData.totalInviteCodes,
      totalApplications: analyticsData.totalApplications,
      totalPurchases: analyticsData.totalPurchases,
      conversionRate: analyticsData.overallConversionRate,
    });

    return json({
      shop,
      analyticsData,
      filters: {
        partnerId: filters.partnerId,
        membershipId: filters.membershipId,
        dateFrom: filters.dateFrom?.toISOString(),
        dateTo: filters.dateTo?.toISOString(),
      },
    });
  } catch (error) {
    console.error("❌ Error loading analytics data:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    return json({
      shop,
      analyticsData: null,
      error: `Failed to load analytics data: ${errorMessage}`,
      filters: {},
    });
  }
};

export default function AnalyticsDashboard() {
  const { shop, analyticsData, error, filters } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleFiltersChange = useCallback((newFilters: {
    dateFrom?: Date;
    dateTo?: Date;
    partnerId?: number;
    membershipId?: string;
  }) => {
    const params = new URLSearchParams();
    
    if (newFilters.partnerId) {
      params.set("partnerId", newFilters.partnerId.toString());
    }
    if (newFilters.membershipId) {
      params.set("membershipId", newFilters.membershipId);
    }
    if (newFilters.dateFrom) {
      params.set("dateFrom", newFilters.dateFrom.toISOString());
    }
    if (newFilters.dateTo) {
      params.set("dateTo", newFilters.dateTo.toISOString());
    }

    setSearchParams(params);
  }, [setSearchParams]);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    // Force a page reload to refresh data
    window.location.reload();
  }, []);

  const handleViewCode = useCallback((codeId: number) => {
    navigate(`/app/invite-codes?highlight=${codeId}`);
  }, [navigate]);

  if (error) {
    return (
      <Page
        title="Analytics Dashboard"
        subtitle={`Analytics for ${shop}`}
        backAction={{ content: "Dashboard", url: "/app" }}
      >
        <Layout>
          <Layout.Section>
            <Banner title="Error loading analytics" tone="critical">
              <p>{error}</p>
              <Box paddingBlockStart="200">
                <Button onClick={() => window.location.reload()}>
                  Try Again
                </Button>
              </Box>
            </Banner>
          </Layout.Section>
        </Layout>
      </Page>
    );
  }

  if (!analyticsData) {
    return (
      <Page
        title="Analytics Dashboard"
        subtitle={`Loading analytics for ${shop}`}
      >
        <Layout>
          <Layout.Section>
            <Card>
              <Box padding="600" textAlign="center">
                <Spinner size="large" />
                <Box paddingBlockStart="400">
                  <Text variant="bodyMd">Loading analytics data...</Text>
                </Box>
              </Box>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    );
  }

  return (
    <Page
      title="Analytics Dashboard"
      subtitle={`Conversion analytics for ${shop}`}
      backAction={{ content: "Dashboard", url: "/app" }}
      primaryAction={{
        content: "Export Data",
        onAction: () => {
          // TODO: Implement data export functionality
          console.log("Export analytics data");
        },
      }}
      secondaryActions={[
        {
          content: isRefreshing ? "Refreshing..." : "Refresh",
          onAction: handleRefresh,
          loading: isRefreshing,
        },
        {
          content: "View All Codes",
          url: "/app/invite-codes",
        },
      ]}
    >
      <Box paddingBlockEnd="400">
        <InlineStack align="end">
          <ThemeToggle />
        </InlineStack>
      </Box>

      <Layout>
        {/* Filters Section */}
        <Layout.Section>
          <AnalyticsFilters
            partnerPerformance={analyticsData.partnerPerformance}
            onFiltersChange={handleFiltersChange}
            loading={isRefreshing}
          />
        </Layout.Section>

        {/* Conversion Metrics Overview */}
        <Layout.Section>
          <ConversionMetrics
            totalInviteCodes={analyticsData.totalInviteCodes}
            activeInviteCodes={analyticsData.activeInviteCodes}
            totalApplications={analyticsData.totalApplications}
            totalPurchases={analyticsData.totalPurchases}
            overallConversionRate={analyticsData.overallConversionRate}
          />
        </Layout.Section>

        {/* Partner Performance and Top Codes */}
        <Layout.Section>
          <InlineStack gap="400" align="start">
            <Box style={{ flex: 1 }}>
              <PartnerPerformanceTable
                partnerPerformance={analyticsData.partnerPerformance}
              />
            </Box>
          </InlineStack>
        </Layout.Section>

        {/* Top Performing Codes */}
        <Layout.Section>
          <TopPerformingCodes
            topPerformingCodes={analyticsData.topPerformingCodes}
            onViewCode={handleViewCode}
          />
        </Layout.Section>

        {/* Recent Activity */}
        <Layout.Section variant="oneThird">
          <RecentActivity
            recentConversions={analyticsData.recentConversions}
          />
        </Layout.Section>

        {/* Summary Information */}
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <Text variant="headingMd" as="h3">
                  Analytics Summary
                </Text>
                <Text variant="bodyMd" as="p">
                  This dashboard shows conversion analytics for your invite codes. 
                  Track how many applications convert to actual purchases, monitor partner performance, 
                  and identify your most effective invite codes.
                </Text>
                <Box paddingBlockStart="200">
                  <InlineStack gap="300">
                    <Button url="/app/invite-codes/new" variant="primary">
                      Create New Code
                    </Button>
                    <Button url="/app/invite-codes/bulk-generate">
                      Bulk Generate
                    </Button>
                    <Button url="/app/settings">
                      Settings
                    </Button>
                  </InlineStack>
                </Box>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
