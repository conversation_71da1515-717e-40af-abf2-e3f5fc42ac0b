import { json, type ActionFunctionArgs } from "@remix-run/node";
import { incrementPurchasesCountByCode, incrementApplicationsCountByCode, getInviteCodeByCode } from "../models/invite-code.server";
import { recordSale } from "../models/sales-tracking.server";
import { cors } from "../utils/cors.server";

/**
 * Simulate order webhook for testing analytics
 * This endpoint simulates the orders/paid webhook to test conversion tracking
 */

export const action = async ({ request }: ActionFunctionArgs) => {
  // Handle CORS preflight requests
  if (request.method === "OPTIONS") {
    return cors(request, new Response(null, { status: 200 }));
  }

  if (request.method !== "POST") {
    return cors(request, json({
      success: false,
      error: "Method not allowed. Use POST to simulate orders.",
    }, { status: 405 }));
  }

  try {
    const body = await request.json();
    const { 
      inviteCode, 
      simulateApplication = true, 
      simulatePurchase = true,
      orderValue = 100,
      customerId = null 
    } = body;

    if (!inviteCode) {
      return cors(request, json({
        success: false,
        error: "Missing required field: inviteCode",
        usage: "POST /api/simulate-order with { inviteCode: 'YOUR_CODE', simulateApplication: true, simulatePurchase: true }"
      }, { status: 400 }));
    }

    console.log(`🛒 Simulating order for invite code: ${inviteCode}`);

    // Get invite code details for proper tagging
    const inviteCodeDetails = await getInviteCodeByCode(inviteCode);
    if (!inviteCodeDetails) {
      return cors(request, json({
        success: false,
        error: `Invite code ${inviteCode} not found`,
        timestamp: new Date().toISOString(),
      }, { status: 404 }));
    }

    const results = {
      inviteCode,
      applicationIncremented: false,
      purchaseIncremented: false,
      orderValue,
      customerId,
      inviteCodeDetails: {
        partnerId: inviteCodeDetails.partnerId,
        partnerName: inviteCodeDetails.partner?.name || null,
        membershipId: inviteCodeDetails.membershipId,
      },
      timestamp: new Date().toISOString(),
    };

    // Simulate application (checkout created)
    if (simulateApplication) {
      try {
        const updatedCode = await incrementApplicationsCountByCode(inviteCode);
        if (updatedCode) {
          results.applicationIncremented = true;
          console.log(`✅ Application count incremented for ${inviteCode}: ${updatedCode.applicationsCount}`);
        } else {
          console.log(`⚠️ Could not increment application count for ${inviteCode}`);
        }
      } catch (error) {
        console.error(`❌ Error incrementing application count:`, error);
      }
    }

    // Simulate purchase (order paid)
    if (simulatePurchase) {
      try {
        const updatedCode = await incrementPurchasesCountByCode(inviteCode);
        if (updatedCode) {
          results.purchaseIncremented = true;
          console.log(`✅ Purchase count incremented for ${inviteCode}: ${updatedCode.purchasesCount}`);

          // Record the sale for analytics
          const saleRecord = await recordSale({
            inviteCode,
            orderValue,
            currency: "USD",
            customerId: customerId || `sim-customer-${Date.now()}`,
            customerEmail: `customer-${Date.now()}@example.com`,
            orderNumber: `#SIM-${Math.floor(Math.random() * 10000)}`,
            shopifyOrderId: `sim-${Math.floor(Math.random() * 1000000)}`,
            shopId: inviteCodeDetails.shopId,
            isSimulated: true,
          });

          if (saleRecord) {
            console.log(`💰 Sale recorded with tag: ${saleRecord.orderTag}`);
            results.saleRecorded = saleRecord;
          }
        } else {
          console.log(`⚠️ Could not increment purchase count for ${inviteCode}`);
        }
      } catch (error) {
        console.error(`❌ Error incrementing purchase count:`, error);
      }
    }

    // Create comprehensive order tags
    const partnerName = results.inviteCodeDetails.partnerName || "UNASSIGNED";
    const orderTag = `${inviteCode}-${partnerName.replace(/\s+/g, '').toUpperCase()}`;
    const membershipTag = `MEMBERSHIP-${results.inviteCodeDetails.membershipId.toUpperCase()}`;

    // Create simulated order data with proper tagging
    const simulatedOrder = {
      id: Math.floor(Math.random() * 1000000),
      order_number: Math.floor(Math.random() * 10000),
      total_price: orderValue.toString(),
      currency: "USD",
      financial_status: "paid",
      fulfillment_status: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      note: `Order from invite code: ${inviteCode} (Partner: ${partnerName})`,
      note_attributes: [
        {
          name: "invite_code",
          value: inviteCode
        },
        {
          name: "partner_name",
          value: partnerName
        },
        {
          name: "partner_id",
          value: results.inviteCodeDetails.partnerId?.toString() || "null"
        },
        {
          name: "membership_id",
          value: results.inviteCodeDetails.membershipId
        },
        {
          name: "order_tag",
          value: orderTag
        },
        {
          name: "simulation",
          value: "true"
        }
      ],
      customer: customerId ? {
        id: customerId,
        email: `customer-${customerId}@example.com`,
        first_name: "Test",
        last_name: "Customer",
        tags: `invite-${inviteCode}, partner-${partnerName.replace(/\s+/g, '').toLowerCase()}, ${membershipTag.toLowerCase()}`
      } : null,
      line_items: [
        {
          id: Math.floor(Math.random() * 100000),
          title: "Premium Product",
          quantity: 1,
          price: orderValue.toString(),
          total_discount: "0.00",
          properties: [
            {
              name: "invite_code",
              value: inviteCode
            },
            {
              name: "partner_name",
              value: partnerName
            },
            {
              name: "source_tag",
              value: orderTag
            }
          ]
        }
      ],
      discount_codes: [],
      // Comprehensive tagging for analytics
      tags: [
        `invite-code-${inviteCode}`,
        `partner-${partnerName.replace(/\s+/g, '').toLowerCase()}`,
        orderTag.toLowerCase(),
        membershipTag.toLowerCase(),
        "simulated-order",
        "analytics-tracking"
      ].join(", ")
    };

    return cors(request, json({
      success: true,
      message: "Order simulation completed successfully",
      results,
      simulatedOrder,
      tagging: {
        orderTag,
        membershipTag,
        partnerName,
        inviteCode,
        allTags: simulatedOrder.tags.split(", ")
      },
      analytics: {
        conversionSimulated: simulateApplication && simulatePurchase,
        applicationOnly: simulateApplication && !simulatePurchase,
        purchaseOnly: !simulateApplication && simulatePurchase,
      },
      note: "Order tagged with: " + orderTag + ". Check your analytics dashboard to see the updated conversion data"
    }));

  } catch (error) {
    console.error("❌ Error in order simulation endpoint:", error);
    return cors(request, json({
      success: false,
      error: "Internal server error during order simulation",
      timestamp: new Date().toISOString(),
    }, { status: 500 }));
  }
};

// Also support GET for simple testing
export const loader = async ({ request }: ActionFunctionArgs) => {
  const url = new URL(request.url);
  const inviteCode = url.searchParams.get("inviteCode");
  
  if (!inviteCode) {
    return cors(request, json({
      success: false,
      error: "Missing inviteCode parameter",
      usage: "GET /api/simulate-order?inviteCode=YOUR_CODE"
    }, { status: 400 }));
  }

  // Simple GET simulation - just increment purchase count
  try {
    const updatedCode = await incrementPurchasesCountByCode(inviteCode);
    
    if (updatedCode) {
      return cors(request, json({
        success: true,
        message: "Simple order simulation completed",
        inviteCode,
        newPurchaseCount: updatedCode.purchasesCount,
        timestamp: new Date().toISOString(),
      }));
    } else {
      return cors(request, json({
        success: false,
        error: "Invite code not found",
        inviteCode,
        timestamp: new Date().toISOString(),
      }, { status: 404 }));
    }
  } catch (error) {
    console.error("❌ Error in simple order simulation:", error);
    return cors(request, json({
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    }, { status: 500 }));
  }
};
