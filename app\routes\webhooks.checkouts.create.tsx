import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { incrementApplicationsCountByCode } from "../models/invite-code.server";

/**
 * Webhook handler for checkouts/create
 * This webhook is triggered when a checkout is created (before payment)
 * We use this to track applications of invite codes (usage without purchase)
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  console.log("🔔 Received checkouts/create webhook");

  try {
    const { topic, shop, session, admin, payload } = await authenticate.webhook(request);
    
    console.log(`🛒 Processing checkout creation for shop: ${shop}`);
    console.log(`🎯 Topic: ${topic}`);

    if (topic !== "CHECKOUTS_CREATE") {
      console.log("❌ Unexpected webhook topic:", topic);
      return json({ success: false, error: "Unexpected webhook topic" }, { status: 400 });
    }

    // Extract checkout data from payload
    const checkout = payload;
    console.log(`🛒 Checkout ID: ${checkout.id}, Total: ${checkout.total_price} ${checkout.currency}`);

    // Look for invite code in checkout attributes, note, or discount codes
    let inviteCode: string | null = null;

    // Check checkout attributes for invite code
    if (checkout.note_attributes && Array.isArray(checkout.note_attributes)) {
      const inviteCodeAttr = checkout.note_attributes.find(
        (attr: any) => attr.name === "invite_code" || attr.name === "inviteCode"
      );
      if (inviteCodeAttr) {
        inviteCode = inviteCodeAttr.value;
        console.log(`🎫 Found invite code in checkout attributes: ${inviteCode}`);
      }
    }

    // Check checkout note for invite code pattern
    if (!inviteCode && checkout.note) {
      const codeMatch = checkout.note.match(/invite[_\s]*code[:\s]*([A-Z0-9]+)/i);
      if (codeMatch) {
        inviteCode = codeMatch[1].toUpperCase();
        console.log(`🎫 Found invite code in checkout note: ${inviteCode}`);
      }
    }

    // Check discount codes (if invite codes are implemented as discount codes)
    if (!inviteCode && checkout.discount_codes && Array.isArray(checkout.discount_codes)) {
      for (const discount of checkout.discount_codes) {
        // Check if this discount code matches an invite code pattern
        if (discount.code && /^[A-Z0-9]{6,12}$/.test(discount.code)) {
          inviteCode = discount.code;
          console.log(`🎫 Found potential invite code in discount codes: ${inviteCode}`);
          break;
        }
      }
    }

    // Check line item properties for invite codes
    if (!inviteCode && checkout.line_items && Array.isArray(checkout.line_items)) {
      for (const lineItem of checkout.line_items) {
        if (lineItem.properties && Array.isArray(lineItem.properties)) {
          const inviteCodeProp = lineItem.properties.find(
            (prop: any) => prop.name === "invite_code" || prop.name === "inviteCode"
          );
          if (inviteCodeProp) {
            inviteCode = inviteCodeProp.value;
            console.log(`🎫 Found invite code in line item properties: ${inviteCode}`);
            break;
          }
        }
      }
    }

    if (!inviteCode) {
      console.log("ℹ️ No invite code found in checkout, skipping tracking");
      return json({ success: true, message: "No invite code found" });
    }

    // Increment the applications count for this invite code
    console.log(`📈 Incrementing applications count for invite code: ${inviteCode}`);
    const updatedCode = await incrementApplicationsCountByCode(inviteCode);

    if (updatedCode) {
      console.log(`✅ Successfully incremented applications count for code ${inviteCode}`);
      console.log(`📊 New applications count: ${updatedCode.applicationsCount}`);
      
      return json({ 
        success: true, 
        message: `Applications count incremented for code ${inviteCode}`,
        code: inviteCode,
        newCount: updatedCode.applicationsCount,
      });
    } else {
      console.log(`⚠️ Invite code ${inviteCode} not found or could not be updated`);
      return json({ 
        success: false, 
        message: `Invite code ${inviteCode} not found`,
        code: inviteCode,
      });
    }

  } catch (error: any) {
    console.error("❌ Error processing checkouts/create webhook:", error);
    return json(
      { 
        success: false, 
        error: "Internal server error",
        details: error.message,
      },
      { status: 500 }
    );
  }
};

/**
 * Alternative webhook handler for carts/update if checkouts/create is not available
 * This can be used as a fallback for tracking invite code applications
 */
export async function handleCartUpdate(request: Request) {
  console.log("🔔 Processing cart update for invite code tracking");

  try {
    const { topic, shop, session, admin, payload } = await authenticate.webhook(request);
    
    const cart = payload;
    console.log(`🛒 Cart updated for shop: ${shop}`);

    // Similar logic to extract invite code from cart
    // This would be implemented based on how invite codes are stored in cart data
    
    return json({ success: true, message: "Cart update processed" });

  } catch (error: any) {
    console.error("❌ Error processing cart update:", error);
    return json(
      { 
        success: false, 
        error: "Internal server error",
        details: error.message,
      },
      { status: 500 }
    );
  }
}

/**
 * Helper function to extract invite code from checkout data
 * This mirrors the logic from the orders webhook but for checkout data
 */
function extractInviteCodeFromCheckout(checkout: any): string | null {
  // Check checkout attributes
  if (checkout.note_attributes && Array.isArray(checkout.note_attributes)) {
    const inviteCodeAttr = checkout.note_attributes.find(
      (attr: any) => 
        attr.name?.toLowerCase().includes("invite") && 
        attr.name?.toLowerCase().includes("code")
    );
    if (inviteCodeAttr?.value) {
      return inviteCodeAttr.value.toString().toUpperCase();
    }
  }

  // Check checkout note
  if (checkout.note) {
    const patterns = [
      /invite[_\s]*code[:\s]*([A-Z0-9]+)/i,
      /code[:\s]*([A-Z0-9]{6,12})/i,
      /referral[:\s]*([A-Z0-9]+)/i,
    ];

    for (const pattern of patterns) {
      const match = checkout.note.match(pattern);
      if (match) {
        return match[1].toUpperCase();
      }
    }
  }

  // Check discount codes
  if (checkout.discount_codes && Array.isArray(checkout.discount_codes)) {
    for (const discount of checkout.discount_codes) {
      if (discount.code && /^[A-Z0-9]{6,12}$/.test(discount.code)) {
        return discount.code.toUpperCase();
      }
    }
  }

  // Check line item properties
  if (checkout.line_items && Array.isArray(checkout.line_items)) {
    for (const lineItem of checkout.line_items) {
      if (lineItem.properties && Array.isArray(lineItem.properties)) {
        const inviteCodeProp = lineItem.properties.find(
          (prop: any) => 
            prop.name?.toLowerCase().includes("invite") && 
            prop.name?.toLowerCase().includes("code")
        );
        if (inviteCodeProp?.value) {
          return inviteCodeProp.value.toString().toUpperCase();
        }
      }
    }
  }

  return null;
}

// Export for potential use in other webhook handlers
export { extractInviteCodeFromCheckout };
