import { PrismaClient } from "@prisma/client";
import type { InviteCode, Partner } from "@prisma/client";

const db = new PrismaClient();

export interface SalesData {
  id: number;
  inviteCode: string;
  partnerId: number | null;
  partnerName: string | null;
  membershipId: string;
  orderValue: number;
  currency: string;
  orderTag: string;
  customerId: string | null;
  customerEmail: string | null;
  orderDate: Date;
  shopId: string;
  orderNumber: string | null;
  shopifyOrderId: string | null;
  isSimulated: boolean;
  createdAt: Date;
}

export interface PartnerSalesMetrics {
  partnerId: number | null;
  partnerName: string;
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  inviteCodesUsed: number;
  conversionRate: number;
  topInviteCodes: Array<{
    code: string;
    orders: number;
    revenue: number;
  }>;
  recentSales: SalesData[];
}

export interface SalesAnalytics {
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  topPartners: PartnerSalesMetrics[];
  topInviteCodes: Array<{
    code: string;
    partnerName: string | null;
    orders: number;
    revenue: number;
    conversionRate: number;
  }>;
  recentSales: SalesData[];
  salesByPeriod: Array<{
    period: string;
    revenue: number;
    orders: number;
  }>;
}

// Record a sale from an invite code
export async function recordSale(data: {
  inviteCode: string;
  orderValue: number;
  currency?: string;
  customerId?: string;
  customerEmail?: string;
  orderNumber?: string;
  shopifyOrderId?: string;
  shopId: string;
  isSimulated?: boolean;
}): Promise<SalesData | null> {
  try {
    // Get invite code details
    const inviteCodeDetails = await db.inviteCode.findUnique({
      where: { code: data.inviteCode },
      include: {
        partner: true,
      },
    });

    if (!inviteCodeDetails) {
      console.error(`❌ Invite code ${data.inviteCode} not found for sale recording`);
      return null;
    }

    // Create order tag
    const partnerName = inviteCodeDetails.partner?.name || "UNASSIGNED";
    const orderTag = `${data.inviteCode}-${partnerName.replace(/\s+/g, '').toUpperCase()}`;

    // Record the sale (this would be a new table in a real implementation)
    // For now, we'll simulate this by creating a comprehensive record
    const saleRecord: SalesData = {
      id: Math.floor(Math.random() * 1000000), // In real app, this would be auto-generated
      inviteCode: data.inviteCode,
      partnerId: inviteCodeDetails.partnerId,
      partnerName: inviteCodeDetails.partner?.name || null,
      membershipId: inviteCodeDetails.membershipId,
      orderValue: data.orderValue,
      currency: data.currency || "USD",
      orderTag,
      customerId: data.customerId || null,
      customerEmail: data.customerEmail || null,
      orderDate: new Date(),
      shopId: data.shopId,
      orderNumber: data.orderNumber || null,
      shopifyOrderId: data.shopifyOrderId || null,
      isSimulated: data.isSimulated || false,
      createdAt: new Date(),
    };

    console.log(`💰 Sale recorded: ${data.orderValue} ${data.currency} for invite code ${data.inviteCode} (Tag: ${orderTag})`);
    
    return saleRecord;
  } catch (error) {
    console.error("❌ Error recording sale:", error);
    return null;
  }
}

// Get sales analytics for a shop
export async function getSalesAnalytics(
  shopId: string,
  dateFrom?: Date,
  dateTo?: Date
): Promise<SalesAnalytics> {
  try {
    // In a real implementation, this would query a sales table
    // For now, we'll generate realistic sample data based on invite codes
    
    const inviteCodes = await db.inviteCode.findMany({
      where: {
        shopId,
        ...(dateFrom && dateTo ? {
          createdAt: {
            gte: dateFrom,
            lte: dateTo,
          },
        } : {}),
      },
      include: {
        partner: true,
        usages: true,
      },
    });

    // Generate realistic sales data
    const salesData: SalesData[] = [];
    let totalRevenue = 0;
    let totalOrders = 0;

    for (const code of inviteCodes) {
      // Simulate sales based on purchases count
      for (let i = 0; i < code.purchasesCount; i++) {
        const orderValue = Math.floor(Math.random() * 300) + 50; // $50-$350
        const partnerName = code.partner?.name || "UNASSIGNED";
        const orderTag = `${code.code}-${partnerName.replace(/\s+/g, '').toUpperCase()}`;
        
        const sale: SalesData = {
          id: salesData.length + 1,
          inviteCode: code.code,
          partnerId: code.partnerId,
          partnerName: code.partner?.name || null,
          membershipId: code.membershipId,
          orderValue,
          currency: "USD",
          orderTag,
          customerId: `customer-${Math.floor(Math.random() * 10000)}`,
          customerEmail: `customer${Math.floor(Math.random() * 10000)}@example.com`,
          orderDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Last 30 days
          shopId,
          orderNumber: `#${Math.floor(Math.random() * 10000)}`,
          shopifyOrderId: `${Math.floor(Math.random() * 1000000)}`,
          isSimulated: true,
          createdAt: new Date(),
        };

        salesData.push(sale);
        totalRevenue += orderValue;
        totalOrders++;
      }
    }

    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calculate partner metrics
    const partnerMetrics = new Map<string, PartnerSalesMetrics>();
    
    for (const sale of salesData) {
      const key = sale.partnerId?.toString() || "unassigned";
      if (!partnerMetrics.has(key)) {
        partnerMetrics.set(key, {
          partnerId: sale.partnerId,
          partnerName: sale.partnerName || "Unassigned",
          totalOrders: 0,
          totalRevenue: 0,
          averageOrderValue: 0,
          inviteCodesUsed: new Set<string>().size,
          conversionRate: 0,
          topInviteCodes: [],
          recentSales: [],
        });
      }
      
      const metrics = partnerMetrics.get(key)!;
      metrics.totalOrders++;
      metrics.totalRevenue += sale.orderValue;
      metrics.recentSales.push(sale);
    }

    // Finalize partner metrics
    const topPartners = Array.from(partnerMetrics.values())
      .map(partner => ({
        ...partner,
        averageOrderValue: partner.totalOrders > 0 ? partner.totalRevenue / partner.totalOrders : 0,
        recentSales: partner.recentSales.slice(-5), // Last 5 sales
      }))
      .sort((a, b) => b.totalRevenue - a.totalRevenue)
      .slice(0, 10);

    // Calculate top invite codes
    const codeMetrics = new Map<string, { code: string; partnerName: string | null; orders: number; revenue: number; }>();
    
    for (const sale of salesData) {
      if (!codeMetrics.has(sale.inviteCode)) {
        codeMetrics.set(sale.inviteCode, {
          code: sale.inviteCode,
          partnerName: sale.partnerName,
          orders: 0,
          revenue: 0,
        });
      }
      
      const metrics = codeMetrics.get(sale.inviteCode)!;
      metrics.orders++;
      metrics.revenue += sale.orderValue;
    }

    const topInviteCodes = Array.from(codeMetrics.values())
      .map(code => ({
        ...code,
        conversionRate: code.orders > 0 ? (code.orders / (code.orders + Math.floor(Math.random() * 5))) * 100 : 0,
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Generate sales by period (last 7 days)
    const salesByPeriod = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
      
      const daySales = salesData.filter(sale => 
        sale.orderDate >= dayStart && sale.orderDate < dayEnd
      );
      
      salesByPeriod.push({
        period: date.toLocaleDateString("en-US", { month: "short", day: "numeric" }),
        revenue: daySales.reduce((sum, sale) => sum + sale.orderValue, 0),
        orders: daySales.length,
      });
    }

    return {
      totalRevenue,
      totalOrders,
      averageOrderValue,
      topPartners,
      topInviteCodes,
      recentSales: salesData.slice(-10).reverse(), // Last 10 sales
      salesByPeriod,
    };

  } catch (error) {
    console.error("❌ Error getting sales analytics:", error);
    return {
      totalRevenue: 0,
      totalOrders: 0,
      averageOrderValue: 0,
      topPartners: [],
      topInviteCodes: [],
      recentSales: [],
      salesByPeriod: [],
    };
  }
}
