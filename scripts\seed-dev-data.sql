-- Supertested Invite Code - Development Seed Data
-- This script creates sample data for development and testing

-- Connect to the database
\c supertested_invite_codes;

-- Create development users table (for reference)
CREATE TABLE IF NOT EXISTS dev_users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'developer',
    shop_domain VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert development users
INSERT INTO dev_users (email, name, role, shop_domain) VALUES
('<EMAIL>', 'Development User', 'developer', 'supersleek-staging.myshopify.com'),
('<EMAIL>', 'Admin User', 'admin', 'supersleek-staging.myshopify.com'),
('<EMAIL>', 'Test User', 'tester', 'supersleek-test.myshopify.com')
ON CONFLICT (email) DO NOTHING;

-- Create development shops table (for reference)
CREATE TABLE IF NOT EXISTS dev_shops (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    plan VARCHAR(50) DEFAULT 'development',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert development shops
INSERT INTO dev_shops (domain, name, email, plan) VALUES
('supersleek-staging.myshopify.com', 'Supersleek Staging', '<EMAIL>', 'development'),
('supersleek-test.myshopify.com', 'Supersleek Test', '<EMAIL>', 'basic'),
('supersleek-demo.myshopify.com', 'Supersleek Demo', '<EMAIL>', 'professional')
ON CONFLICT (domain) DO NOTHING;

-- Create sample invite codes (these will be created via Prisma migrations)
-- This is just for reference - actual data will be created by the seed script

-- Development configuration
CREATE TABLE IF NOT EXISTS dev_config (
    id SERIAL PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert development configuration
INSERT INTO dev_config (key, value, description) VALUES
('app_version', '1.0.0-dev', 'Current application version'),
('default_shop', 'supersleek-staging.myshopify.com', 'Default shop for development'),
('max_codes_per_shop', '1000', 'Maximum invite codes per shop'),
('default_code_expiry_days', '30', 'Default expiry period for new codes'),
('enable_debug_logging', 'true', 'Enable debug logging in development'),
('enable_test_mode', 'true', 'Enable test mode features'),
('sample_multipass_secret', 'dev_multipass_secret_for_testing', 'Sample Multipass secret for development')
ON CONFLICT (key) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description;

-- Create development API keys table (for testing API endpoints)
CREATE TABLE IF NOT EXISTS dev_api_keys (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    permissions TEXT[],
    shop_domain VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert development API keys
INSERT INTO dev_api_keys (name, api_key, permissions, shop_domain) VALUES
('Development Key', 'dev_api_key_12345', ARRAY['read', 'write'], 'supersleek-staging.myshopify.com'),
('Test Key', 'test_api_key_67890', ARRAY['read'], 'supersleek-test.myshopify.com'),
('Demo Key', 'demo_api_key_abcde', ARRAY['read', 'write', 'admin'], 'supersleek-demo.myshopify.com')
ON CONFLICT (api_key) DO NOTHING;

-- Create sample customer data for testing
CREATE TABLE IF NOT EXISTS dev_customers (
    id SERIAL PRIMARY KEY,
    customer_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    shop_domain VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert sample customers
INSERT INTO dev_customers (customer_id, email, first_name, last_name, shop_domain) VALUES
('customer_001', '<EMAIL>', 'John', 'Doe', 'supersleek-staging.myshopify.com'),
('customer_002', '<EMAIL>', 'Jane', 'Smith', 'supersleek-staging.myshopify.com'),
('customer_003', '<EMAIL>', 'Bob', 'Wilson', 'supersleek-test.myshopify.com'),
('customer_004', '<EMAIL>', 'Alice', 'Brown', 'supersleek-demo.myshopify.com'),
('customer_005', '<EMAIL>', 'Charlie', 'Davis', 'supersleek-staging.myshopify.com')
ON CONFLICT (customer_id) DO NOTHING;

-- Grant permissions to invite_app user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO invite_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO invite_app;

-- Create indexes for better performance in development
CREATE INDEX IF NOT EXISTS idx_dev_users_email ON dev_users(email);
CREATE INDEX IF NOT EXISTS idx_dev_shops_domain ON dev_shops(domain);
CREATE INDEX IF NOT EXISTS idx_dev_customers_email ON dev_customers(email);
CREATE INDEX IF NOT EXISTS idx_dev_customers_shop ON dev_customers(shop_domain);

-- Display setup completion message
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Development database setup completed successfully!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Development Users: %', (SELECT COUNT(*) FROM dev_users);
    RAISE NOTICE 'Development Shops: %', (SELECT COUNT(*) FROM dev_shops);
    RAISE NOTICE 'Sample Customers: %', (SELECT COUNT(*) FROM dev_customers);
    RAISE NOTICE 'API Keys: %', (SELECT COUNT(*) FROM dev_api_keys);
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Run: npx prisma migrate dev --name init';
    RAISE NOTICE '2. Run: npx prisma db seed';
    RAISE NOTICE '3. Access pgAdmin at: http://localhost:8080';
    RAISE NOTICE '4. Start development: npm run dev';
    RAISE NOTICE '=================================================';
END $$;
