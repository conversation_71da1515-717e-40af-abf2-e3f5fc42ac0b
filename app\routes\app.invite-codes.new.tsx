import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { useActionData, useNavigation, useSubmit, useLoaderData } from "@remix-run/react";
import { Page, Layout, Card, Text, Box, Banner, InlineStack } from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import { createInviteCode } from "../models/invite-code.server";
import { createPartner, getPartnersForDropdown } from "../models/partner.server";
import { getMembershipTagsForDropdown } from "../models/membership-tag.server";
import { InviteCodeForm, type InviteCodeFormData } from "../components/InviteCodeForm";
import { ThemeToggle } from "../components/UI/ThemeToggle";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Load membership plans and partners for the form
    const [membershipPlans, partners] = await Promise.all([
      getMembershipTagsForDropdown(shop),
      getPartnersForDropdown(shop),
    ]);

    return json({
      shop,
      membershipPlans,
      partners: partners.map(p => ({ value: p.id.toString(), label: p.name })),
    });
  } catch (error) {
    console.error("Error loading form data:", error);
    return json({
      shop,
      membershipPlans: [],
      partners: [],
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  console.log("💾 Creating new invite code...");
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    const formData = await request.formData();
    const data = Object.fromEntries(formData) as any;
    const action = data.action || "create";

    console.log("📝 Form data received:", data);
    console.log("🏪 Shop:", shop);
    console.log("👤 Session ID:", session.id);
    console.log("🎬 Action:", action);

    // Handle create partner action
    if (action === "createPartner") {
      const partnerData = {
        name: data.name?.toString().trim() || "",
        email: data.email?.toString().trim() || undefined,
        company: data.company?.toString().trim() || undefined,
        shopId: shop,
      };

      if (!partnerData.name) {
        return json(
          { error: "Partner name is required", success: false },
          { status: 400 }
        );
      }

      const newPartner = await createPartner(partnerData);
      console.log("✅ Successfully created partner:", newPartner);

      return json({
        success: true,
        partner: newPartner,
        message: "Partner created successfully",
      });
    }

    // Handle create invite code action (default)
    // Validate and parse form data
    const inviteCodeData: InviteCodeFormData = {
      code: data.code?.toString().trim().toUpperCase() || "",
      maxUses: parseInt(data.maxUses?.toString() || "1"),
      expiresAt: data.expiresAt?.toString() || undefined,
      isActive: data.isActive === "true",
      partnerId: data.partnerId ? parseInt(data.partnerId.toString()) : undefined,
      membershipId: data.membershipId?.toString() || "",
    };

    // Validate required fields
    if (!inviteCodeData.code) {
      return json(
        { error: "Invite code is required", success: false },
        { status: 400 }
      );
    }

    if (inviteCodeData.maxUses < 1) {
      return json(
        { error: "Maximum uses must be at least 1", success: false },
        { status: 400 }
      );
    }

    if (!inviteCodeData.membershipId) {
      return json(
        { error: "Membership plan is required", success: false },
        { status: 400 }
      );
    }

    // Validate code format
    if (!/^[A-Z0-9]+$/.test(inviteCodeData.code)) {
      return json(
        { error: "Invite code can only contain uppercase letters and numbers", success: false },
        { status: 400 }
      );
    }

    // Parse expiry date if provided
    let expiresAt: Date | undefined;
    if (inviteCodeData.expiresAt) {
      expiresAt = new Date(inviteCodeData.expiresAt);
      if (expiresAt <= new Date()) {
        return json(
          { error: "Expiry date must be in the future", success: false },
          { status: 400 }
        );
      }
    }

    // Create the invite code
    console.log("🚀 Creating invite code with data:", {
      code: inviteCodeData.code,
      maxUses: inviteCodeData.maxUses,
      expiresAt,
      createdBy: session.id,
      shopId: shop,
    });

    const newCode = await createInviteCode({
      code: inviteCodeData.code,
      maxUses: inviteCodeData.maxUses,
      expiresAt,
      createdBy: session.id, // or use a proper user identifier
      shopId: shop,
      partnerId: inviteCodeData.partnerId,
      membershipId: inviteCodeData.membershipId,
    });

    console.log("✅ Successfully created invite code:", newCode);

    // Redirect to the codes list with success message
    return redirect(`/app/invite-codes?created=${newCode.code}`);
    
  } catch (error: any) {
    console.error("❌ Error creating invite code:", error);

    // Handle specific database errors
    if (error.code === "P2002") {
      return json(
        { error: "An invite code with this name already exists. Please try a different code.", success: false },
        { status: 400 }
      );
    }

    // Handle our custom duplicate error
    if (error.message && error.message.includes("already exists")) {
      return json(
        { error: error.message, success: false },
        { status: 400 }
      );
    }

    // Handle unique code generation failure
    if (error.message && error.message.includes("Failed to generate unique invite code")) {
      return json(
        { error: "Unable to generate a unique invite code. Please try again or specify a custom code.", success: false },
        { status: 500 }
      );
    }

    return json(
      { error: "Failed to create invite code. Please try again.", success: false },
      { status: 500 }
    );
  }
};

export default function NewInviteCode() {
  const actionData = useActionData<typeof action>();
  const loaderData = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const submit = useSubmit();
  const isSubmitting = navigation.state === "submitting";

  const handleSubmit = (data: InviteCodeFormData) => {
    console.log("🚀 Form submission triggered with data:", data);

    // Use Remix's submit function for proper form submission
    const formData = new FormData();
    formData.append("action", "create");
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, value?.toString() || "");
    });

    console.log("📤 Submitting form with fields:", Object.keys(data));
    submit(formData, { method: "POST" });
  };

  const handleCreatePartner = async (partnerData: { name: string; email?: string; company?: string }) => {
    console.log("📤 Creating new partner:", partnerData);

    const formData = new FormData();
    formData.append("action", "createPartner");
    Object.entries(partnerData).forEach(([key, value]) => {
      if (value) {
        formData.append(key, value);
      }
    });

    submit(formData, { method: "POST" });
  };

  const handleCancel = () => {
    window.history.back();
  };

  return (
    <Page
      title="Create New Invite Code"
      subtitle="Set up a new invite code for your customers"
      backAction={{
        content: "Back to Invite Codes",
        url: "/app/invite-codes",
      }}
    >
      <Box paddingBlockEnd="400">
        <InlineStack align="end">
          <ThemeToggle />
        </InlineStack>
      </Box>

      <Layout>
        <Layout.Section variant="oneHalf">
          <InviteCodeForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={isSubmitting}
            error={actionData && 'error' in actionData ? actionData.error : undefined}
            mode="create"
            membershipPlans={loaderData.membershipPlans}
            partners={loaderData.partners}
            onCreatePartner={handleCreatePartner}
          />
        </Layout.Section>

        <Layout.Section variant="oneHalf">
          <Card>
            <Box padding="400">
              <Text variant="headingMd" as="h3">
                Invite Code Tips
              </Text>
              <Box paddingBlockStart="400">
                <Text variant="bodyMd" as="p">
                  Here are some best practices for creating invite codes:
                </Text>
                <Box paddingBlockStart="300">
                  <ul>
                    <li><strong>Keep it simple:</strong> Use short, memorable codes like "WELCOME10" or "SAVE25"</li>
                    <li><strong>Make it relevant:</strong> Include the discount amount or purpose in the code</li>
                    <li><strong>Set limits:</strong> Control usage with maximum use counts and expiry dates</li>
                    <li><strong>Track performance:</strong> Monitor which codes are most effective</li>
                  </ul>
                </Box>
              </Box>
            </Box>
          </Card>

          <Box paddingBlockStart="400">
            <Card>
              <Box padding="400">
                <Text variant="headingMd" as="h3">
                  Code Format Rules
                </Text>
                <Box paddingBlockStart="400">
                  <Text variant="bodyMd" as="p">
                    Invite codes must follow these rules:
                  </Text>
                  <Box paddingBlockStart="300">
                    <ul>
                      <li>Only uppercase letters (A-Z) and numbers (0-9)</li>
                      <li>Minimum 3 characters long</li>
                      <li>No spaces or special characters</li>
                      <li>Must be unique across your store</li>
                    </ul>
                  </Box>
                </Box>
              </Box>
            </Card>
          </Box>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
