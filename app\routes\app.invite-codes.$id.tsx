import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, <PERSON>, useNavigate } from "@remix-run/react";
import { 
  Page, 
  Layout, 
  Card, 
  Text, 
  Box, 
  InlineStack, 
  Button,
  DataTable,
  Badge,
  Divider
} from "@shopify/polaris";
import { EditIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import { getInviteCodeById } from "../models/invite-code.server";
import { ThemeToggle } from "../components/UI/ThemeToggle";
import { formatDate, formatDateTime, isDateExpired } from "../utils/dateFormatter";
// Removed problematic import

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  const id = parseInt(params.id!);

  console.log(`🔍 Loading invite code ${id} details...`);

  try {
    const inviteCode = await getInviteCodeById(id, shop);
    
    if (!inviteCode) {
      throw new Response("Invite code not found", { status: 404 });
    }

    console.log(`✅ Found invite code: ${inviteCode.code} with ${inviteCode.usages.length} usages`);
    
    return json({ 
      inviteCode,
      shop 
    });
  } catch (error) {
    console.error("❌ Error loading invite code:", error);
    throw new Response("Invite code not found", { status: 404 });
  }
};

export default function ViewInviteCode() {
  const { inviteCode } = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  // Debug function for navigation
  const handleEditClick = (source: string) => {
    const editUrl = `/app/invite-codes/edit/${inviteCode.id}`;
    console.log(`🔧 Edit button clicked from: ${source}`);
    console.log("📍 Current URL:", window.location.href);
    console.log("🎯 Target URL:", editUrl);
    console.log("🆔 Invite Code ID:", inviteCode.id);
    console.log("🔍 Navigate function:", typeof navigate);

    try {
      console.log("🚀 Attempting navigation with useNavigate...");
      navigate(editUrl);
      console.log("✅ Navigation called successfully");

    } catch (error) {
      console.error("❌ Navigation error:", error);
      console.log("🔄 Fallback: Using window.location...");
      window.location.href = editUrl;
    }
  };

  // Prepare usage data for the table
  const usageRows = inviteCode.usages.map((usage: any) => [
    formatDateTime(usage.usedAt),
    usage.customerId || "Anonymous",
    usage.ipAddress || "Unknown",
    usage.userAgent ? usage.userAgent.substring(0, 50) + "..." : "Unknown"
  ]);

  const actualUsageCount = inviteCode.usages?.length || 0;
  const usagePercentage = inviteCode.maxUses > 0
    ? Math.round((actualUsageCount / inviteCode.maxUses) * 100)
    : 0;

  return (
    <Page
      title={`Invite Code: ${inviteCode.code}`}
      subtitle="View details and usage history"
      backAction={{
        content: "Back to Invite Codes",
        url: "/app/invite-codes",
      }}
      primaryAction={{
        content: "Edit",
        icon: EditIcon,
        onAction: () => {
          console.log("🔧 PRIMARY EDIT BUTTON CLICKED!");
          console.log("📊 Primary action triggered");
          handleEditClick("Primary Action");
        },
      }}
    >
      <Box paddingBlockEnd="400">
        <InlineStack align="end">
          <ThemeToggle />
        </InlineStack>
      </Box>

      <Layout>
        <Layout.Section variant="oneHalf">
          <Card>
            <Text variant="headingLg" as="h2">
              Code Details
            </Text>
            <Box paddingBlockStart="400">
              <InlineStack gap="400" wrap={false}>
                <Box minWidth="120px">
                  <Text variant="headingXl" as="h1">
                    {inviteCode.code}
                  </Text>
                </Box>
                <InlineStack gap="200">
                  <Badge tone={inviteCode.isActive ? "success" : "critical"} size="small">
                    {inviteCode.isActive ? "Active" : "Inactive"}
                  </Badge>
                  <Badge
                    tone={inviteCode.usedCount >= inviteCode.maxUses ? "critical" :
                          (inviteCode.usedCount / inviteCode.maxUses) >= 0.8 ? "warning" : "success"}
                    size="small"
                  >
                    {inviteCode.usedCount}/{inviteCode.maxUses} uses
                  </Badge>
                  {inviteCode.expiresAt ? (
                    <Badge
                      tone={isDateExpired(inviteCode.expiresAt) ? "critical" : "success"}
                      size="small"
                    >
                      {isDateExpired(inviteCode.expiresAt) ? "Expired" :
                       `Expires ${formatDate(inviteCode.expiresAt)}`}
                    </Badge>
                  ) : (
                    <Badge tone="info" size="small">No expiry</Badge>
                  )}
                </InlineStack>
              </InlineStack>
            </Box>

            <Box paddingBlockStart="400">
              <Divider />
            </Box>

            <Box paddingBlockStart="400">
              <InlineStack gap="800" wrap>
                <Box>
                  <Text variant="headingSm" as="h3">
                    Usage
                  </Text>
                  <Text as="p" variant="bodyLg">
                    {actualUsageCount} / {inviteCode.maxUses} ({usagePercentage}%)
                  </Text>
                </Box>

                <Box>
                  <Text variant="headingSm" as="h3">
                    Status
                  </Text>
                  <Text as="p" variant="bodyLg">
                    {inviteCode.isActive ? "Active" : "Inactive"}
                  </Text>
                </Box>

                {inviteCode.expiresAt && (
                  <Box>
                    <Text variant="headingSm" as="h3">
                      Expires
                    </Text>
                    <Text as="p" variant="bodyLg">
                      {formatDate(inviteCode.expiresAt)}
                    </Text>
                  </Box>
                )}

                <Box>
                  <Text variant="headingSm" as="h3">
                    Created
                  </Text>
                  <Text as="p" variant="bodyLg">
                    {formatDate(inviteCode.createdAt)}
                  </Text>
                </Box>
              </InlineStack>
            </Box>
          </Card>
        </Layout.Section>

        <Layout.Section variant="oneHalf">
          <Card>
            <Text variant="headingMd" as="h2">
              Quick Actions
            </Text>
            <Box paddingBlockStart="400">
              <InlineStack gap="200">
                <Link
                  to={`/app/invite-codes/edit/${inviteCode.id}`}
                  onClick={(e) => {
                    console.log("🔗 LINK EDIT CODE CLICKED!");
                    console.log("📍 Link target:", `/app/invite-codes/edit/${inviteCode.id}`);
                    console.log("🎯 Link event:", e);
                    console.log("🔍 Event type:", e.type);
                    console.log("🔍 Event target:", e.target);
                    console.log("🔍 Current target:", e.currentTarget);

                    // Also trigger our debug function
                    handleEditClick("Link Component");
                  }}
                >
                  <Button
                    variant="primary"
                    icon={EditIcon}
                    onClick={(e) => {
                      console.log("🔘 BUTTON INSIDE LINK CLICKED!");
                      console.log("🎯 Button event:", e);
                      console.log("🔍 Button event type:", e.type);

                      // Prevent default to see if that helps
                      e.preventDefault();
                      e.stopPropagation();

                      console.log("🚀 Manually triggering navigation from button...");
                      handleEditClick("Button inside Link");
                    }}
                  >
                    Edit Code
                  </Button>
                </Link>
                <Button
                  variant="secondary"
                  onClick={() => {
                    console.log("📋 Copy button clicked!");
                    navigator.clipboard.writeText(inviteCode.code);
                    console.log("✅ Code copied to clipboard:", inviteCode.code);
                    // You could add a toast notification here
                  }}
                >
                  Copy Code
                </Button>
              </InlineStack>
            </Box>
          </Card>

          <Box paddingBlockStart="400">
            <Card>
              <Text variant="headingMd" as="h2">
                Usage Statistics
              </Text>
              <Box paddingBlockStart="400">
                <Text as="p">
                  <strong>Total Uses:</strong> {actualUsageCount}
                </Text>
                <Text as="p">
                  <strong>Remaining Uses:</strong> {inviteCode.maxUses - actualUsageCount}
                </Text>
                <Text as="p">
                  <strong>Usage Rate:</strong> {usagePercentage}%
                </Text>
              </Box>
            </Card>
          </Box>
        </Layout.Section>
      </Layout>

      {inviteCode.usages && inviteCode.usages.length > 0 && (
        <Box paddingBlockStart="500">
          <Card>
            <Text variant="headingMd" as="h2">
              Usage History ({inviteCode.usages.length} total)
            </Text>
            <Box paddingBlockStart="400">
              <DataTable
                columnContentTypes={['text', 'text', 'text', 'text']}
                headings={['Date & Time', 'Customer ID', 'IP Address', 'User Agent']}
                rows={usageRows}
                pagination={{
                  hasNext: false,
                  hasPrevious: false,
                  onNext: () => {},
                  onPrevious: () => {},
                }}
              />
            </Box>
          </Card>
        </Box>
      )}

      {(!inviteCode.usages || inviteCode.usages.length === 0) && (
        <Box paddingBlockStart="500">
          <Card>
            <Text variant="headingMd" as="h2">
              Usage History
            </Text>
            <Box paddingBlockStart="400">
              <Text as="p" tone="subdued">
                This invite code hasn't been used yet.
              </Text>
            </Box>
          </Card>
        </Box>
      )}
    </Page>
  );
}
