import { useState, useEffect } from "react";
import { But<PERSON>, Icon } from "@shopify/polaris";
import { MoonIcon, SunIcon } from "@shopify/polaris-icons";
import { useClientOnly } from "../../hooks/useClientOnly";

export function ThemeToggle() {
  const [theme, setTheme] = useState<"light" | "dark">("light");
  const isClient = useClientOnly();

  // Initialize theme only on client side
  useEffect(() => {
    if (!isClient) return;

    const savedTheme = localStorage.getItem("theme") as "light" | "dark" | null;
    const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
    const initialTheme = savedTheme || (prefersDark ? "dark" : "light");

    console.log("🎨 Theme initialized:", { savedTheme, prefersDark, initialTheme });
    setTheme(initialTheme);
    applyTheme(initialTheme);
  }, [isClient]);

  // Listen for system theme changes
  useEffect(() => {
    if (!isClient) return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleChange = (e: MediaQueryListEvent) => {
      if (!localStorage.getItem("theme")) {
        const newTheme = e.matches ? "dark" : "light";
        console.log("🎨 System theme changed:", newTheme);
        setTheme(newTheme);
        applyTheme(newTheme);
      }
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [isClient]);

  const applyTheme = (newTheme: "light" | "dark") => {
    if (typeof window === 'undefined') return;

    console.log("🎨 Applying theme:", newTheme);
    document.documentElement.setAttribute("data-theme", newTheme);

    // Update Polaris theme classes
    if (newTheme === "dark") {
      document.documentElement.classList.add("Polaris-Dark");
      document.documentElement.classList.remove("Polaris-Light");
    } else {
      document.documentElement.classList.add("Polaris-Light");
      document.documentElement.classList.remove("Polaris-Dark");
    }

    // Enhanced dark mode styling
    if (newTheme === "dark") {
      document.body.style.backgroundColor = "#1a1a1a";
      document.body.style.color = "#e1e3e5";

      // Add custom CSS variables for better dark mode
      document.documentElement.style.setProperty("--p-color-bg", "#1a1a1a");
      document.documentElement.style.setProperty("--p-color-bg-surface", "#2a2a2a");
      document.documentElement.style.setProperty("--p-color-bg-surface-secondary", "#333333");
      document.documentElement.style.setProperty("--p-color-text", "#e1e3e5");
      document.documentElement.style.setProperty("--p-color-text-secondary", "#b5b5b5");
      document.documentElement.style.setProperty("--p-color-border", "#404040");
      document.documentElement.style.setProperty("--p-color-border-secondary", "#333333");
    } else {
      document.body.style.backgroundColor = "#ffffff";
      document.body.style.color = "#202223";

      // Reset to light mode variables
      document.documentElement.style.removeProperty("--p-color-bg");
      document.documentElement.style.removeProperty("--p-color-bg-surface");
      document.documentElement.style.removeProperty("--p-color-bg-surface-secondary");
      document.documentElement.style.removeProperty("--p-color-text");
      document.documentElement.style.removeProperty("--p-color-text-secondary");
      document.documentElement.style.removeProperty("--p-color-border");
      document.documentElement.style.removeProperty("--p-color-border-secondary");
    }
  };

  const toggleTheme = () => {
    if (!isClient) return;

    const newTheme = theme === "light" ? "dark" : "light";
    console.log("🎨 Theme toggled:", { from: theme, to: newTheme });
    setTheme(newTheme);
    applyTheme(newTheme);
    localStorage.setItem("theme", newTheme);
  };

  // Don't render until mounted to avoid hydration mismatch
  if (!isClient) {
    return (
      <Button
        variant="tertiary"
        size="large"
        icon={SunIcon}
        accessibilityLabel="Toggle theme"
        disabled
      />
    );
  }

  return (
    <Button
      variant="tertiary"
      size="large"
      icon={theme === "light" ? MoonIcon : SunIcon}
      onClick={toggleTheme}
      accessibilityLabel={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
      ariaPressed={theme === "dark"}
    />
  );
}
