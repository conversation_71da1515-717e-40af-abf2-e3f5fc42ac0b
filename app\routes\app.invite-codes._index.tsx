import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useSearchParams } from "@remix-run/react";
import { Page, Box, InlineStack, Banner } from "@shopify/polaris";
import { PlusIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import { getInviteCodes, updateInviteCode, deleteInviteCode } from "../models/invite-code.server";
import { getPartnersForDropdown } from "../models/partner.server";
import { getMembershipTagsForDropdown } from "../models/membership-tag.server";
import { InviteCodeList, type InviteCodeFilters } from "../components/InviteCodeList";
import { ThemeToggle } from "../components/UI/ThemeToggle";
import { useEffect, useState } from "react";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  const formData = await request.formData();
  const action = formData.get("action") as string;

  console.log(`🔧 Action: ${action}`);

  try {
    switch (action) {
      case "toggle":
        const codeId = parseInt(formData.get("codeId") as string);
        const isActive = formData.get("isActive") === "true";
        console.log(`🔄 Toggling code ${codeId} to ${isActive ? "active" : "inactive"}`);
        await updateInviteCode(codeId, shop, { isActive });
        return json({ success: true, message: `Code ${isActive ? "activated" : "deactivated"} successfully` });

      case "delete":
        const singleCodeId = parseInt(formData.get("codeId") as string);
        console.log(`🗑️ Deleting code ${singleCodeId}`);
        await deleteInviteCode(singleCodeId, shop);
        return json({ success: true, message: "Code deleted successfully" });

      case "bulkDelete":
        const codeIds = formData.get("codeIds") as string;
        const idsToDelete = codeIds.split(",").map(id => parseInt(id.trim()));
        console.log(`🗑️ Bulk deleting codes: ${idsToDelete.join(", ")}`);

        // Delete all codes in parallel
        await Promise.all(idsToDelete.map(id => deleteInviteCode(id, shop)));

        return json({
          success: true,
          message: `Successfully deleted ${idsToDelete.length} invite codes`
        });

      default:
        return json({ success: false, message: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error(`❌ Error performing action ${action}:`, error);
    return json({ success: false, message: "Operation failed" }, { status: 500 });
  }
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  console.log("🔍 Loading invite codes list...");
  const { admin, session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Get actual invite codes from database
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "20");
    const search = url.searchParams.get("search") || "";
    const status = url.searchParams.getAll("status");
    const partnerId = url.searchParams.get("partnerId");
    const membershipId = url.searchParams.get("membershipId");
    const dateFrom = url.searchParams.get("dateFrom");
    const dateTo = url.searchParams.get("dateTo");
    const sortBy = url.searchParams.get("sortBy") || "newest";

    console.log(`📊 Fetching codes for shop: ${shop}, page: ${page}, limit: ${limit}, search: "${search}", status: [${status.join(", ")}], partner: ${partnerId}, membership: ${membershipId}, dateRange: ${dateFrom} to ${dateTo}, sortBy: ${sortBy}`);

    // Fetch filter options and codes in parallel
    const [result, partners, membershipTags] = await Promise.all([
      getInviteCodes({
        shopId: shop,
        page,
        limit,
        search,
        isActive: status.length > 0 ? status.includes("active") : undefined,
        partnerId: partnerId ? parseInt(partnerId) : undefined,
        membershipId: membershipId || undefined,
        dateFrom: dateFrom || undefined,
        dateTo: dateTo || undefined,
        sortBy: sortBy,
      }),
      getPartnersForDropdown(shop),
      getMembershipTagsForDropdown(shop),
    ]);

    console.log(`✅ Found ${result.codes.length} codes, total: ${result.pagination.total}`);

    return json({
      ...result,
      partners: partners.map(p => ({ value: p.id.toString(), label: p.name })),
      membershipPlans: membershipTags,
    });
  } catch (error) {
    console.error("❌ Error loading invite codes:", error);
    // Return empty data on error with same structure
    return json({
      codes: [],
      pagination: { page: 1, limit: 20, total: 0, pages: 0 },
      partners: [],
      membershipPlans: [],
    });
  }
};

export default function InviteCodesIndex() {
  const loaderData = useLoaderData<typeof loader>();
  const { codes, pagination } = loaderData;
  const submit = useSubmit();
  const [searchParams] = useSearchParams();
  const [showSuccessBanner, setShowSuccessBanner] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  console.log("🎯 Rendering invite codes page with:", {
    codesCount: codes.length,
    pagination,
    firstCode: codes[0] ? { id: codes[0].id, code: codes[0].code } : null
  });

  // Check for success messages from URL parameters
  useEffect(() => {
    const createdCode = searchParams.get("created");
    const updatedCode = searchParams.get("updated");

    if (createdCode) {
      setSuccessMessage(`✅ Invite code "${createdCode}" created successfully!`);
      setShowSuccessBanner(true);

      // Remove the parameter from URL after showing the message
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete("created");
      window.history.replaceState({}, "", newUrl.toString());

      // Auto-hide after 5 seconds
      setTimeout(() => setShowSuccessBanner(false), 5000);
    } else if (updatedCode) {
      setSuccessMessage(`✅ Invite code "${updatedCode}" updated successfully!`);
      setShowSuccessBanner(true);

      // Remove the parameter from URL after showing the message
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete("updated");
      window.history.replaceState({}, "", newUrl.toString());

      // Auto-hide after 5 seconds
      setTimeout(() => setShowSuccessBanner(false), 5000);
    }
  }, [searchParams]);

  const handlePageChange = (page: number) => {
    const url = new URL(window.location.href);
    url.searchParams.set("page", page.toString());
    window.location.href = url.pathname + url.search;
  };

  const handleFiltersChange = (filters: InviteCodeFilters) => {
    const url = new URL(window.location.href);

    // Reset to page 1 when filters change
    url.searchParams.set("page", "1");

    // Set search parameter
    if (filters.search) {
      url.searchParams.set("search", filters.search);
    } else {
      url.searchParams.delete("search");
    }

    // Set status parameters
    url.searchParams.delete("status");
    if (filters.status && filters.status.length > 0) {
      filters.status.forEach(status => {
        url.searchParams.append("status", status);
      });
    }

    // Set sortBy parameter
    if (filters.sortBy) {
      url.searchParams.set("sortBy", filters.sortBy);
    } else {
      url.searchParams.delete("sortBy");
    }

    // Set partnerId parameter
    if (filters.partnerId) {
      url.searchParams.set("partnerId", filters.partnerId);
    } else {
      url.searchParams.delete("partnerId");
    }

    // Set membershipId parameter
    if (filters.membershipId) {
      url.searchParams.set("membershipId", filters.membershipId);
    } else {
      url.searchParams.delete("membershipId");
    }

    // Set date range parameters
    if (filters.dateFrom) {
      url.searchParams.set("dateFrom", filters.dateFrom);
    } else {
      url.searchParams.delete("dateFrom");
    }

    if (filters.dateTo) {
      url.searchParams.set("dateTo", filters.dateTo);
    } else {
      url.searchParams.delete("dateTo");
    }

    window.location.href = url.pathname + url.search;
  };



  const handleDelete = (id: number) => {
    if (confirm("Are you sure you want to delete this invite code? This action cannot be undone.")) {
      const formData = new FormData();
      formData.append("action", "delete");
      formData.append("codeId", id.toString());

      submit(formData, { method: "post" });
    }
  };

  const handleToggleActive = (code: any) => {
    const formData = new FormData();
    formData.append("action", "toggle");
    formData.append("codeId", code.id.toString());
    formData.append("isActive", (!code.isActive).toString());

    submit(formData, { method: "post" });
  };

  const handleBulkDelete = (codeIds: number[]) => {
    if (confirm(`Are you sure you want to delete ${codeIds.length} invite codes? This action cannot be undone.`)) {
      const formData = new FormData();
      formData.append("action", "bulkDelete");
      formData.append("codeIds", codeIds.join(","));

      submit(formData, { method: "post" });
    }
  };

  return (
    <Page
      title="Invite Codes"
      subtitle={`${pagination.total} total codes`}
      primaryAction={{
        content: "Create invite code",
        icon: PlusIcon,
        url: "/app/invite-codes/new",
      }}
      secondaryActions={[
        {
          content: "Bulk Generate",
          url: "/app/invite-codes/bulk-generate",
        },
      ]}
    >
      <Box paddingBlockEnd="400">
        <InlineStack align="end">
          <ThemeToggle />
        </InlineStack>
      </Box>

      {showSuccessBanner && (
        <Box paddingBlockEnd="400">
          <Banner
            title="Success"
            tone="success"
            onDismiss={() => setShowSuccessBanner(false)}
          >
            <p>{successMessage}</p>
          </Banner>
        </Box>
      )}

      <InviteCodeList
        codes={codes as any}
        pagination={pagination}
        onPageChange={handlePageChange}
        onDelete={handleDelete}
        onBulkDelete={handleBulkDelete}
        onFiltersChange={handleFiltersChange}
        onToggleActive={handleToggleActive}
        partners={(loaderData as any).partners || []}
        membershipPlans={(loaderData as any).membershipPlans || []}
      />
    </Page>
  );
}
