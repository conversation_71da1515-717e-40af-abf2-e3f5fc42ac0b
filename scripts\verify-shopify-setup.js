#!/usr/bin/env node

/**
 * Supersleek Invite Code - Shopify Setup Verification
 * This script verifies that all Shopify configurations are correct for 2025 API
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 Supersleek Invite Code - Shopify Setup Verification');
console.log('====================================================\n');

let hasErrors = false;
let hasWarnings = false;

// Load environment variables
function loadEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const match = line.match(/^([^#][^=]*?)=(.*)$/);
    if (match) {
      const key = match[1].trim();
      const value = match[2].trim().replace(/^["']|["']$/g, '');
      env[key] = value;
    }
  });
  
  return env;
}

const env = loadEnvFile('.env');

// Test 1: Check Shopify API Configuration
console.log('🔧 Checking Shopify API Configuration...');

const requiredShopifyVars = [
  'SHOPIFY_API_KEY',
  'SHOPIFY_API_SECRET',
  'SHOPIFY_SCOPES',
  'SHOPIFY_APP_SESSION_SECRET'
];

const optionalShopifyVars = [
  'SHOPIFY_ACCESS_TOKEN',
  'SHOPIFY_STOREFRONT_ACCESS_TOKEN'
];

requiredShopifyVars.forEach(varName => {
  if (env[varName] && env[varName] !== 'your_api_key_here' && env[varName] !== 'your_api_secret_here') {
    console.log(`  ✅ ${varName} is configured`);
  } else {
    console.log(`  ❌ ${varName} is missing or using placeholder value`);
    hasErrors = true;
  }
});

// Test 2: Verify Scopes for 2025 API
console.log('\n📋 Checking Shopify Scopes for 2025 API...');

const requiredScopes = [
  'read_customers',
  'write_customers',
  'read_customer_events',
  'write_customer_events',
  'read_orders',
  'write_orders',
  'read_products',
  'write_products'
];

const recommendedScopes = [
  'unauthenticated_read_customers',
  'unauthenticated_write_customers'
];

const configuredScopes = env.SHOPIFY_SCOPES ? env.SHOPIFY_SCOPES.split(',').map(s => s.trim()) : [];

requiredScopes.forEach(scope => {
  if (configuredScopes.includes(scope)) {
    console.log(`  ✅ ${scope}`);
  } else {
    console.log(`  ❌ ${scope} - Missing!`);
    hasErrors = true;
  }
});

// Check for recommended scopes
console.log('\n📋 Checking Recommended Scopes for Customer Auth...');
recommendedScopes.forEach(scope => {
  if (configuredScopes.includes(scope)) {
    console.log(`  ✅ ${scope}`);
  } else {
    console.log(`  ⚠️  ${scope} - Recommended for better customer auth`);
    hasWarnings = true;
  }
});

// Check for deprecated scopes
const deprecatedScopes = ['read_script_tags', 'write_script_tags'];
const foundDeprecated = configuredScopes.filter(scope => deprecatedScopes.includes(scope));

if (foundDeprecated.length > 0) {
  console.log(`  ⚠️  Deprecated scopes found: ${foundDeprecated.join(', ')}`);
  hasWarnings = true;
}

// Test 3: Check Shop Domain Configuration
console.log('\n🏪 Checking Shop Domain Configuration...');

const shopDomains = [
  { key: 'DEV_SHOP_DOMAIN', expected: 'supersleek-staging.myshopify.com' },
  { key: 'TEST_SHOP_DOMAIN', expected: 'supersleek-test.myshopify.com' }
];

shopDomains.forEach(({ key, expected }) => {
  if (env[key] === expected) {
    console.log(`  ✅ ${key}: ${env[key]}`);
  } else if (env[key]) {
    console.log(`  ⚠️  ${key}: ${env[key]} (expected: ${expected})`);
    hasWarnings = true;
  } else {
    console.log(`  ❌ ${key} is not configured`);
    hasErrors = true;
  }
});

// Test 4: Check API Version in Code
console.log('\n🔄 Checking API Version Configuration...');

if (fs.existsSync('app/shopify.server.ts')) {
  const shopifyServerContent = fs.readFileSync('app/shopify.server.ts', 'utf8');
  
  if (shopifyServerContent.includes('ApiVersion.January25')) {
    console.log('  ✅ API Version set to January 2025');
  } else if (shopifyServerContent.includes('ApiVersion.')) {
    console.log('  ⚠️  API Version found but not January 2025');
    hasWarnings = true;
  } else {
    console.log('  ❌ API Version not found in shopify.server.ts');
    hasErrors = true;
  }
  
  if (shopifyServerContent.includes('SHOPIFY_SCOPES')) {
    console.log('  ✅ Scopes configuration updated');
  } else {
    console.log('  ❌ Scopes configuration not updated');
    hasErrors = true;
  }
} else {
  console.log('  ❌ shopify.server.ts not found');
  hasErrors = true;
}

// Test 5: Check Customer Auth Configuration
console.log('\n🔐 Checking Customer Auth Configuration...');

// Check for access tokens (optional but recommended)
optionalShopifyVars.forEach(varName => {
  if (env[varName] && env[varName] !== 'your_token_here') {
    console.log(`  ✅ ${varName} is configured`);
  } else {
    console.log(`  ⚠️  ${varName} not configured (recommended for customer auth)`);
    hasWarnings = true;
  }
});

// Note about Multipass
console.log('  ℹ️  Multipass not required (Shopify Basic plan compatible)');
console.log('  ℹ️  Using Customer Account API + Storefront API approach');

// Test 6: Check Database Configuration
console.log('\n🗄️  Checking Database Configuration...');

if (env.DATABASE_URL && env.DATABASE_URL.includes('supersleek_invite_codes')) {
  console.log('  ✅ Database URL configured for supersleek_invite_codes');
} else {
  console.log('  ❌ Database URL not properly configured');
  hasErrors = true;
}

// Test 7: Check Seed Data Configuration
console.log('\n🌱 Checking Seed Data Configuration...');

if (fs.existsSync('prisma/seed.ts')) {
  const seedContent = fs.readFileSync('prisma/seed.ts', 'utf8');
  
  if (seedContent.includes('supersleek-staging.myshopify.com')) {
    console.log('  ✅ Seed data updated for supersleek-staging shop');
  } else {
    console.log('  ⚠️  Seed data may not be updated for new shop domain');
    hasWarnings = true;
  }
} else {
  console.log('  ❌ Seed file not found');
  hasErrors = true;
}

// Test 8: Check Customer Auth Utility
console.log('\n🛠️  Checking Customer Auth Utility...');

if (fs.existsSync('app/utils/customer-auth.server.ts')) {
  const customerAuthContent = fs.readFileSync('app/utils/customer-auth.server.ts', 'utf8');

  if (customerAuthContent.includes('CustomerAuthManager')) {
    console.log('  ✅ Customer Auth Manager implemented');
  } else {
    console.log('  ⚠️  Customer Auth Manager may need updates');
    hasWarnings = true;
  }

  if (customerAuthContent.includes('createCustomerWithInviteCode')) {
    console.log('  ✅ Customer creation with invite code implemented');
  } else {
    console.log('  ⚠️  Customer creation function may be missing');
    hasWarnings = true;
  }
} else {
  console.log('  ❌ Customer auth utility not found');
  hasErrors = true;
}

// Check for API endpoints
if (fs.existsSync('app/routes/api.create-customer.ts')) {
  console.log('  ✅ Customer creation API endpoint exists');
} else {
  console.log('  ❌ Customer creation API endpoint missing');
  hasErrors = true;
}

// Summary and Recommendations
console.log('\n📋 Setup Verification Summary');
console.log('============================');

if (hasErrors) {
  console.log('❌ Setup has critical errors that need to be fixed!');
  console.log('\n🔧 Required Actions:');
  console.log('1. Update missing environment variables in .env file');
  console.log('2. Configure proper Shopify API credentials');
  console.log('3. Ensure all required scopes are included');
  console.log('4. Fix any missing files or configurations');
} else if (hasWarnings) {
  console.log('⚠️  Setup is mostly complete but has some warnings');
  console.log('\n🔧 Recommended Actions:');
  console.log('1. Configure access tokens for customer authentication');
  console.log('2. Add recommended scopes for better customer auth');
  console.log('3. Test the setup with your Shopify store');
  console.log('4. Review Customer Auth documentation: ./SHOPIFY_BASIC_CUSTOMER_AUTH.md');
} else {
  console.log('✅ Setup verification passed! Your configuration looks good.');
  console.log('\n🚀 Next Steps:');
  console.log('1. Start your database: npm run db:start');
  console.log('2. Run migrations: npx prisma migrate dev --name init');
  console.log('3. Seed database: npx prisma db seed');
  console.log('4. Start development: npm run dev');
}

console.log('\n📚 Additional Resources:');
console.log('- Customer Auth Guide: ./SHOPIFY_BASIC_CUSTOMER_AUTH.md');
console.log('- Shopify Scopes: ./SHOPIFY_SCOPES.md');
console.log('- Database Setup: ./LOCAL_POSTGRES_SETUP.md');
console.log('- Setup Guide: ./SETUP_COMPLETE.md');

console.log('\n🔗 Important URLs for supersleek-staging.myshopify.com:');
console.log('- Validation Page: http://localhost:3000/validate?shop=supersleek-staging.myshopify.com');
console.log('- API Docs: http://localhost:3000/api/docs');
console.log('- Admin Dashboard: http://localhost:3000/app');

if (hasErrors) {
  process.exit(1);
} else {
  console.log('\n🎉 Ready for development!');
  process.exit(0);
}
