# Shopify API Scopes for Supersleek Invite Code App

This document explains the Shopify API scopes required for the Supersleek Invite Code app and why each scope is necessary.

## Current Scopes Configuration

```env
SHOPIFY_SCOPES="read_customers,write_customers,read_customer_events,write_customer_events,read_orders,write_orders,read_products,write_products"
```

## Scope Breakdown

### Customer Management Scopes

#### `read_customers`
- **Purpose**: Read customer information for validation and analytics
- **Used for**: 
  - Retrieving customer details when validating invite codes
  - Displaying customer information in usage analytics
  - Checking if customer already exists before Multipass login

#### `write_customers`
- **Purpose**: Create and update customer accounts
- **Used for**:
  - Creating new customer accounts via Multipass integration
  - Updating customer information when they use invite codes
  - Managing customer tags and metadata for invite code tracking

#### `read_customer_events`
- **Purpose**: Access customer activity and events
- **Used for**:
  - Tracking customer engagement with invite codes
  - Analytics on customer behavior patterns
  - Understanding customer journey after invite code usage

#### `write_customer_events`
- **Purpose**: Create custom customer events
- **Used for**:
  - Recording invite code usage events in customer timeline
  - Creating custom events for analytics and tracking
  - Logging customer interactions with the invite system

### Order Management Scopes

#### `read_orders`
- **Purpose**: Access order information for analytics
- **Used for**:
  - Tracking conversion rates from invite codes to orders
  - Analyzing the effectiveness of different invite codes
  - Generating reports on invite code ROI

#### `write_orders`
- **Purpose**: Create and modify orders (if needed)
- **Used for**:
  - Applying discounts or special pricing for invite code users
  - Creating draft orders with invite code benefits
  - Modifying orders to include invite code tracking

### Product Management Scopes

#### `read_products`
- **Purpose**: Access product information
- **Used for**:
  - Displaying available products to invite code users
  - Creating product-specific invite codes
  - Analytics on which products are popular with invited customers

#### `write_products`
- **Purpose**: Create and modify products
- **Used for**:
  - Creating special products or variants for invite code users
  - Managing product visibility for invited customers
  - Setting up exclusive products for invite code holders

## API Version

```typescript
apiVersion: ApiVersion.January25  // 2025-01 API version
```

The app uses the **2025-01 API version** which includes:
- Enhanced customer management capabilities
- Improved Multipass integration
- Better event tracking and analytics
- Updated GraphQL schema with new fields

## Multipass Integration Requirements

For Multipass to work properly, you need:

1. **Customer Scopes**: `read_customers`, `write_customers`
2. **Multipass Enabled**: In Shopify Admin > Settings > Customer accounts
3. **Multipass Secret**: Configured in environment variables
4. **Customer Account Settings**: Set to "Accounts are optional" or "Accounts are required"

## Scope Usage in Key Features

### Invite Code Validation
- `read_customers` - Check if customer exists
- `read_customer_events` - Check previous invite code usage

### Multipass Login
- `write_customers` - Create customer account if doesn't exist
- `write_customer_events` - Log login event

### Analytics Dashboard
- `read_customers` - Customer demographics
- `read_orders` - Conversion tracking
- `read_customer_events` - Usage patterns

### Admin Interface
- `read_products` - Product selection for targeted codes
- `write_products` - Create exclusive products (optional)

## Security Considerations

### Minimal Scope Principle
- Only request scopes that are actually used
- Regularly review and remove unused scopes
- Document the purpose of each scope

### Data Protection
- Customer data is only accessed when necessary
- All customer interactions are logged for transparency
- Data retention policies are followed

## Testing Scopes

During development, you can test scope functionality:

```bash
# Test customer read access
curl -X GET "https://supersleek-staging.myshopify.com/admin/api/2025-01/customers.json" \
  -H "X-Shopify-Access-Token: YOUR_ACCESS_TOKEN"

# Test customer creation
curl -X POST "https://supersleek-staging.myshopify.com/admin/api/2025-01/customers.json" \
  -H "X-Shopify-Access-Token: YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"customer": {"email": "<EMAIL>", "first_name": "Test"}}'
```

## Scope Updates

If you need to add or remove scopes:

1. **Update `.env` file**:
   ```env
   SHOPIFY_SCOPES="new,scope,list"
   ```

2. **Update Partners Dashboard**:
   - Go to your app in Partners Dashboard
   - Update the scopes in App Setup
   - Save changes

3. **Reinstall the app**:
   - Uninstall from test store
   - Reinstall to get new permissions

4. **Test new functionality**:
   - Verify new scopes work as expected
   - Update documentation

## Common Issues

### Scope Permission Denied
- **Error**: `insufficient_scope`
- **Solution**: Check if scope is included in SHOPIFY_SCOPES
- **Fix**: Add missing scope and reinstall app

### Multipass Not Working
- **Error**: Customer creation fails
- **Solution**: Ensure `write_customers` scope is enabled
- **Fix**: Check Multipass is enabled in Shopify Admin

### Analytics Missing Data
- **Error**: Customer events not showing
- **Solution**: Verify `read_customer_events` scope
- **Fix**: Check API version compatibility

## Production Considerations

### Scope Review
- Audit scopes before production deployment
- Remove any development-only scopes
- Document business justification for each scope

### Compliance
- Ensure scopes comply with data protection regulations
- Document data usage for each scope
- Implement proper data retention policies

### Monitoring
- Monitor scope usage in production
- Track API rate limits
- Log scope-related errors for debugging

## Future Scope Requirements

As the app evolves, you might need additional scopes:

- `read_inventory` - For inventory-based invite codes
- `write_inventory` - For inventory management
- `read_discounts` - For discount code integration
- `write_discounts` - For automatic discount creation
- `read_analytics` - For advanced analytics
- `read_marketing_events` - For marketing integration

Remember to update this documentation when adding new scopes!
