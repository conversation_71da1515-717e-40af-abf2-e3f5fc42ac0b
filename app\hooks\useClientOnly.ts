import { useState } from 'react';
import { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';

/**
 * A hook that returns true only after the component has mounted on the client.
 * This is useful for preventing hydration mismatches when rendering client-only content.
 * 
 * @returns boolean - true if the component has mounted on the client, false otherwise
 */
export function useClientOnly(): boolean {
  const [hasMounted, setHasMounted] = useState(false);

  useIsomorphicLayoutEffect(() => {
    setHasMounted(true);
  }, []);

  return hasMounted;
}
