import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
// Temporarily commented out to fix import issue
// import { validateAndUseInviteCode } from "~/models/invite-code.server";
// import { generateCustomerLoginUrl } from "~/utils/customer-auth.server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Temporary validation function
async function validateAndUseInviteCode(
  code: string,
  metadata?: {
    customerId?: string;
    ipAddress?: string;
    userAgent?: string;
  }
): Promise<{ valid: boolean; reason?: string; inviteCode?: any }> {
  try {
    const inviteCode = await prisma.inviteCode.findUnique({
      where: { code },
    });

    if (!inviteCode) {
      return { valid: false, reason: "Code not found" };
    }

    if (!inviteCode.isActive) {
      return { valid: false, reason: "Code is inactive" };
    }

    if (inviteCode.expiresAt && inviteCode.expiresAt < new Date()) {
      return { valid: false, reason: "Code has expired" };
    }

    if (inviteCode.usedCount >= inviteCode.maxUses) {
      return { valid: false, reason: "Code has reached maximum uses" };
    }

    // Record the usage and increment the used count
    await prisma.$transaction([
      prisma.usage.create({
        data: {
          inviteCodeId: inviteCode.id,
          customerId: metadata?.customerId || "unknown",
          ipAddress: metadata?.ipAddress || "unknown",
          userAgent: metadata?.userAgent || "unknown",
        },
      }),
      prisma.inviteCode.update({
        where: { id: inviteCode.id },
        data: { usedCount: { increment: 1 } },
      }),
    ]);

    return { valid: true, inviteCode };
  } catch (error) {
    console.error("Error validating invite code:", error);
    return { valid: false, reason: "Database error" };
  }
}

interface ValidateInviteRequest {
  code: string;
  shopDomain?: string;
  customerData?: {
    email?: string;
    firstName?: string;
    lastName?: string;
    returnTo?: string;
  };
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
    customerId?: string;
  };
}

interface ValidateInviteResponse {
  success: boolean;
  message: string;
  data?: {
    code: string;
    remainingUses: number;
    maxUses: number;
    expiresAt?: string;
    loginUrl?: string;
  };
  error?: string;
}

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(ip: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now();
  const key = ip;
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= limit) {
    return false;
  }

  record.count++;
  return true;
}

function getClientIP(request: Request): string {
  const forwarded = request.headers.get("x-forwarded-for");
  const realIP = request.headers.get("x-real-ip");
  const cfConnectingIP = request.headers.get("cf-connecting-ip");
  
  if (forwarded) {
    return forwarded.split(",")[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return "unknown";
}

// Handle both GET and POST requests
export const loader = async ({ request }: LoaderFunctionArgs) => {
  return handleValidateRequest(request);
};

export const action = async ({ request }: ActionFunctionArgs) => {
  return handleValidateRequest(request);
};

async function handleValidateRequest(request: Request) {
  const clientIP = getClientIP(request);
  const userAgent = request.headers.get("user-agent") || "unknown";

  // Check rate limiting
  if (!checkRateLimit(clientIP, 20, 60000)) { // 20 requests per minute
    return json<ValidateInviteResponse>(
      {
        success: false,
        message: "Rate limit exceeded. Please try again later.",
        error: "RATE_LIMIT_EXCEEDED",
      },
      { 
        status: 429,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        }
      }
    );
  }

  try {
    let requestData: ValidateInviteRequest;

    if (request.method === "GET") {
      // Handle GET request with query parameters
      const url = new URL(request.url);
      const code = url.searchParams.get("code");
      const shopDomain = url.searchParams.get("shopDomain");
      const email = url.searchParams.get("email");
      const firstName = url.searchParams.get("firstName");
      const lastName = url.searchParams.get("lastName");
      const returnTo = url.searchParams.get("returnTo");

      if (!code) {
        return json<ValidateInviteResponse>(
          {
            success: false,
            message: "Invite code is required",
            error: "MISSING_CODE",
          },
          { 
            status: 400,
            headers: {
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
              "Access-Control-Allow-Headers": "Content-Type",
            }
          }
        );
      }

      requestData = {
        code,
        shopDomain: shopDomain || undefined,
        customerData: {
          email: email || undefined,
          firstName: firstName || undefined,
          lastName: lastName || undefined,
          returnTo: returnTo || undefined,
        },
        metadata: {
          ipAddress: clientIP,
          userAgent,
        },
      };
    } else {
      // Handle POST request with JSON body
      try {
        requestData = await request.json();
      } catch (error) {
        return json<ValidateInviteResponse>(
          {
            success: false,
            message: "Invalid JSON in request body",
            error: "INVALID_JSON",
          },
          { 
            status: 400,
            headers: {
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
              "Access-Control-Allow-Headers": "Content-Type",
            }
          }
        );
      }

      if (!requestData.code) {
        return json<ValidateInviteResponse>(
          {
            success: false,
            message: "Invite code is required",
            error: "MISSING_CODE",
          },
          { 
            status: 400,
            headers: {
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
              "Access-Control-Allow-Headers": "Content-Type",
            }
          }
        );
      }

      // Add metadata if not provided
      requestData.metadata = {
        ipAddress: clientIP,
        userAgent,
        ...requestData.metadata,
      };
    }

    // Validate and use the invite code
    const result = await validateAndUseInviteCode(
      requestData.code.toUpperCase(),
      requestData.metadata
    );

    if (!result.valid) {
      return json<ValidateInviteResponse>(
        {
          success: false,
          message: result.reason || "Invalid invite code",
          error: "INVALID_CODE",
        },
        { 
          status: 400,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type",
          }
        }
      );
    }

    const inviteCode = result.inviteCode!;
    let loginUrl: string | undefined;

    // Generate customer login URL if shop domain is provided - temporarily disabled
    if (requestData.shopDomain) {
      try {
        // loginUrl = generateCustomerLoginUrl(
        //   requestData.shopDomain,
        //   requestData.code,
        //   requestData.customerData || {}
        // );
        // Temporary fallback URL
        loginUrl = `https://${requestData.shopDomain}/account/login`;
      } catch (error) {
        console.error("Error generating login URL:", error);
        // Continue without login URL if generation fails
      }
    }

    return json<ValidateInviteResponse>(
      {
        success: true,
        message: "Invite code validated successfully",
        data: {
          code: inviteCode.code,
          remainingUses: inviteCode.maxUses - inviteCode.usedCount,
          maxUses: inviteCode.maxUses,
          expiresAt: inviteCode.expiresAt?.toISOString(),
          loginUrl,
        },
      },
      {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        }
      }
    );

  } catch (error) {
    console.error("Error validating invite code:", error);
    
    return json<ValidateInviteResponse>(
      {
        success: false,
        message: "Internal server error",
        error: "INTERNAL_ERROR",
      },
      { 
        status: 500,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        }
      }
    );
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
