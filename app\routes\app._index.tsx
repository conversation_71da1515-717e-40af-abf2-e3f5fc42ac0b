import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Box,
  InlineStack,
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import { getUsageStats } from "../models/invite-code.server";
import { StatsGrid } from "../components/Dashboard/StatsCard";
import { ActivityFeed, QuickStats } from "../components/Dashboard/ActivityFeed";
import { QuickCreateForm } from "../components/InviteCodeForm";
import { ThemeToggle } from "../components/UI/ThemeToggle";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  console.log("🏠 Loading dashboard...");
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  console.log(`📊 Getting stats for shop: ${shop}`);
  // Get usage statistics for the dashboard
  const stats = await getUsageStats(shop);
  console.log("📈 Dashboard stats:", stats);

  // Calculate quick stats
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
  const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

  // Filter recent usages for quick stats
  const todayUsages = stats.recentUsages.filter(usage => 
    new Date(usage.usedAt) >= today
  ).length;
  
  const weekUsages = stats.recentUsages.filter(usage => 
    new Date(usage.usedAt) >= weekAgo
  ).length;
  
  const monthUsages = stats.recentUsages.filter(usage => 
    new Date(usage.usedAt) >= monthAgo
  ).length;

  return json({ 
    shop,
    stats,
    quickStats: {
      todayUsages,
      weekUsages,
      monthUsages,
    }
  });
};

export default function Dashboard() {
  const { shop, stats, quickStats } = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  const handleQuickCreate = async (data: { code: string; maxUses: number }) => {
    console.log("⚡ Quick create triggered:", data);

    try {
      // Create form data to submit to the create action
      const formData = new FormData();
      formData.append("code", data.code);
      formData.append("maxUses", data.maxUses.toString());
      formData.append("expiresAt", ""); // No expiration for quick create

      console.log("📤 Submitting quick create form data:", {
        code: data.code,
        maxUses: data.maxUses
      });

      // Submit to the create invite code action
      const response = await fetch("/app/invite-codes/new", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        console.log("✅ Quick create successful!");
        // Navigate to invite codes list with success message using Remix navigation
        navigate(`/app/invite-codes?created=${data.code}`);
      } else {
        console.error("❌ Quick create failed:", response.status);
        alert("Failed to create invite code. Please try again.");
      }
    } catch (error) {
      console.error("❌ Quick create error:", error);
      alert("Error creating invite code. Please try again.");
    }
  };

  return (
    <Page
      title="Invite Code Dashboard"
      subtitle={`Managing codes for ${shop}`}
      primaryAction={{
        content: "Create Invite Code",
        url: "/app/invite-codes/new",
      }}
      secondaryActions={[
        {
          content: "View All Codes",
          url: "/app/invite-codes",
        },
      ]}
    >
      <Box paddingBlockEnd="400">
        <InlineStack align="end">
          <ThemeToggle />
        </InlineStack>
      </Box>

      <Layout>
        <Layout.Section>
          <StatsGrid stats={stats} />
        </Layout.Section>

        <Layout.Section variant="oneThird">
          <Box paddingBlockEnd="400">
            <QuickCreateForm 
              onSubmit={handleQuickCreate}
              loading={false}
            />
          </Box>
          <QuickStats {...quickStats} />
        </Layout.Section>

        <Layout.Section variant="oneHalf">
          <ActivityFeed
            activities={stats.recentUsages.map(usage => ({
              ...usage,
              customerId: usage.customerId || undefined,
              ipAddress: usage.ipAddress || undefined,
              userAgent: usage.userAgent || undefined
            }))}
            title="Recent Activity"
          />
        </Layout.Section>

        <Layout.Section>
          <Card>
            <Box padding="400">
              <Text variant="headingMd" as="h3">
                Getting Started
              </Text>
              <Box paddingBlockStart="400">
                <Text variant="bodyMd" as="p">
                  Welcome to your Invite Code Dashboard! Here you can:
                </Text>
                <Box paddingBlockStart="200">
                  <ul>
                    <li>Create and manage invite codes</li>
                    <li>Track usage statistics</li>
                    <li>Monitor recent activity</li>
                    <li>Set expiry dates and usage limits</li>
                  </ul>
                </Box>
              </Box>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
