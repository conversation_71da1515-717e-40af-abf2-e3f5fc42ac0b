generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Boolean   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified Boolean?  @default(false)
}

model Partner {
  id          Int          @id @default(autoincrement())
  name        String       @db.VarChar(255)
  email       String?      @db.VarChar(255)
  phone       String?      @db.VarChar(50)
  company     String?      @db.VarChar(255)
  notes       String?
  isActive    Boolean      @default(true) @map("is_active")
  createdAt   DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  shopId      String       @map("shop_id") @db.Var<PERSON><PERSON>(255)
  inviteCodes InviteCode[]

  @@index([shopId])
  @@index([name])
  @@map("partners")
}

model InviteCode {
  id                Int       @id @default(autoincrement())
  code              String    @unique @db.VarChar(32)
  maxUses           Int       @default(1) @map("max_uses")
  expiresAt         DateTime? @map("expires_at") @db.Timestamptz(6)
  isActive          Boolean   @default(true) @map("is_active")
  createdAt         DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  createdBy         String    @map("created_by") @db.VarChar(255)
  shopId            String    @map("shop_id") @db.VarChar(255)
  applicationsCount Int       @default(0) @map("applications_count")
  purchasesCount    Int       @default(0) @map("purchases_count")
  partnerId         Int?      @map("partner_id")
  membershipId      String    @map("membership_id") @db.VarChar(255)
  partner           Partner?  @relation(fields: [partnerId], references: [id])
  usages            Usage[]

  @@index([code])
  @@index([shopId])
  @@index([expiresAt])
  @@index([partnerId])
  @@index([membershipId])
  @@map("invite_codes")
}

model Usage {
  id           Int        @id @default(autoincrement())
  inviteCodeId Int        @map("invite_code_id")
  usedAt       DateTime   @default(now()) @map("used_at") @db.Timestamptz(6)
  customerId   String?    @map("customer_id") @db.VarChar(255)
  ipAddress    String?    @map("ip_address") @db.VarChar(45)
  userAgent    String?    @map("user_agent")
  inviteCode   InviteCode @relation(fields: [inviteCodeId], references: [id], onDelete: Cascade)

  @@index([inviteCodeId])
  @@index([usedAt])
  @@map("usages")
}

model MembershipTag {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  customerTag String   @map("customer_tag") @db.VarChar(255)
  orderTag    String?  @map("order_tag") @db.VarChar(255)
  shop        String   @db.VarChar(255)
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)

  @@unique([shop, customerTag])
  @@index([shop])
  @@map("membership_tags")
}

model AppSettings {
  id                      Int      @id @default(autoincrement())
  shop                    String   @unique @db.VarChar(255)
  dateFormat              String   @default("YYYY-MM-DD") @map("date_format") @db.VarChar(50)
  timeFormat              String   @default("24h") @map("time_format") @db.VarChar(10)
  timezone                String   @default("UTC") @db.VarChar(100)
  defaultExpiryDays       Int?     @map("default_expiry_days")
  enableEmailNotifications Boolean @default(true) @map("enable_email_notifications")
  maxCodesPerBatch        Int      @default(100) @map("max_codes_per_batch")
  createdAt               DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt               DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)

  @@index([shop])
  @@map("app_settings")
}
