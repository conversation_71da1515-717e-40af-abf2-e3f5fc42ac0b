import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { createCustomerWithInviteCode } from "../services/customer-creation.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  // Only allow POST requests
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Get shop context from headers or query params
    const url = new URL(request.url);
    const shopDomain = url.searchParams.get('shop') || request.headers.get('x-shop-domain');

    if (!shopDomain) {
      return json({
        success: false,
        error: "Shop domain is required"
      }, { status: 400 });
    }

    // Authenticate with Shopify
    const { admin, session } = await authenticate.public.appProxy(request);

    const body = await request.json();
    const { inviteCode, email, firstName, lastName, acceptsMarketing, returnTo } = body;

    // Validate required fields
    if (!inviteCode) {
      return json({
        success: false,
        error: "Invite code is required"
      }, { status: 400 });
    }

    // Get client metadata
    const clientIP = request.headers.get("x-forwarded-for") ||
                    request.headers.get("x-real-ip") ||
                    "unknown";
    const userAgent = request.headers.get("user-agent") || "unknown";

    // Create customer with invite code validation and tag application
    const result = await createCustomerWithInviteCode({
      inviteCode,
      email,
      firstName,
      lastName,
      acceptsMarketing,
      shop: session.shop,
      admin,
      metadata: {
        ipAddress: clientIP,
        userAgent,
      },
    });

    if (!result.success) {
      return json({
        success: false,
        error: result.error || "Failed to create customer account"
      }, { status: 400 });
    }

    return json({
      success: true,
      customer: result.customer,
      inviteCodeApplied: result.inviteCodeApplied,
      membershipApplied: result.membershipApplied,
      loginUrl: result.loginUrl || `https://${shopDomain}/account/login`,
      message: `Customer account created successfully. ${result.membershipApplied ? 'Membership tag applied.' : ''} ${result.inviteCodeApplied ? 'Invite code tag applied.' : ''}`.trim()
    });

  } catch (error) {
    console.error("Error creating customer:", error);
    return json({ 
      success: false, 
      error: "Internal server error" 
    }, { status: 500 });
  }
};

// Handle preflight requests for CORS
export const loader = async () => {
  return json({ message: "Use POST method to create customer" }, { status: 405 });
};
