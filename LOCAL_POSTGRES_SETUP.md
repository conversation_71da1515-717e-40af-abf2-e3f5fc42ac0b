# Local PostgreSQL Setup Guide

This guide will help you set up PostgreSQL locally for the Supersleek Invite Code app. We'll use <PERSON><PERSON> for the easiest setup experience.

## Prerequisites

You need to have Docker installed on your Windows machine.

### Install Docker Desktop

1. **Download Docker Desktop**: Go to https://www.docker.com/products/docker-desktop/
2. **Install**: Run the installer and follow the setup wizard
3. **Start Docker**: Launch Docker Desktop and wait for it to start
4. **Verify Installation**: Open PowerShell and run:
   ```powershell
   docker --version
   ```

## Option 1: Docker Setup (Recommended)

### Step 1: Start PostgreSQL with Docker

Open PowerShell in your project directory and run:

```powershell
# Start PostgreSQL and pgAdmin
docker-compose up -d

# Check if containers are running
docker-compose ps
```

This will:
- Start PostgreSQL on port 5432
- Start pgAdmin (web interface) on port 8080
- Create the database `supertested_invite_codes`
- Set up user `invite_app` with password `dev_password_2024`

### Step 2: Verify Database Connection

```powershell
# Test connection using Docker
docker-compose exec postgres psql -U invite_app -d supertested_invite_codes -c "SELECT 'Connection successful!' as status;"
```

### Step 3: Access pgAdmin (Optional)

1. Open your browser and go to: http://localhost:8080
2. Login with:
   - Email: `<EMAIL>`
   - Password: `admin123`
3. Add server connection:
   - Host: `postgres` (container name)
   - Port: `5432`
   - Database: `supersleek_invite_codes`
   - Username: `invite_app`
   - Password: `dev_password_2024`

### Step 4: Run Database Migrations

```powershell
# Generate Prisma client
npx prisma generate

# Run migrations to create tables
npx prisma migrate dev --name init

# Seed database with sample data (optional)
npx prisma db seed
```

### Step 5: Verify Setup

```powershell
# Run our setup test
node scripts/test-setup.js

# Check database with Prisma Studio
npx prisma studio
```

## Option 2: Direct PostgreSQL Installation

If you prefer to install PostgreSQL directly on Windows:

### Step 1: Download and Install

1. Go to https://www.postgresql.org/download/windows/
2. Download the installer for Windows
3. Run the installer and follow these settings:
   - Port: `5432`
   - Superuser password: Choose a secure password
   - Locale: Default

### Step 2: Create Database and User

Open pgAdmin or use psql command line:

```sql
-- Create database
CREATE DATABASE supertested_invite_codes;

-- Create user
CREATE USER invite_app WITH PASSWORD 'dev_password_2024';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE supertested_invite_codes TO invite_app;
```

### Step 3: Update Connection String

Update your `.env` file:
```env
DATABASE_URL="postgresql://invite_app:dev_password_2024@localhost:5432/supertested_invite_codes?schema=public"
```

## Managing Your Local Database

### Start/Stop Docker Containers

```powershell
# Start containers
docker-compose up -d

# Stop containers
docker-compose down

# Stop and remove all data (reset database)
docker-compose down -v
```

### Useful Commands

```powershell
# View container logs
docker-compose logs postgres

# Connect to PostgreSQL directly
docker-compose exec postgres psql -U invite_app -d supersleek_invite_codes

# Backup database
docker-compose exec postgres pg_dump -U invite_app supersleek_invite_codes > backup.sql

# Restore database
docker-compose exec -T postgres psql -U invite_app supersleek_invite_codes < backup.sql
```

### Database Management

```powershell
# Reset database (removes all data)
npx prisma migrate reset

# View current migration status
npx prisma migrate status

# Generate new migration after schema changes
npx prisma migrate dev --name your_migration_name

# Open Prisma Studio (database browser)
npx prisma studio
```

## Troubleshooting

### Port 5432 Already in Use

If you get a port conflict error:

```powershell
# Check what's using port 5432
netstat -ano | findstr :5432

# Stop the conflicting service or change the port in docker-compose.yml
```

### Connection Refused

1. Make sure Docker containers are running:
   ```powershell
   docker-compose ps
   ```

2. Check container logs:
   ```powershell
   docker-compose logs postgres
   ```

3. Verify the DATABASE_URL in your `.env` file

### Permission Denied

Make sure the database user has proper permissions:

```sql
GRANT ALL PRIVILEGES ON DATABASE supersleek_invite_codes TO invite_app;
GRANT ALL ON SCHEMA public TO invite_app;
```

## Performance Tips

### For Development

- Keep Docker containers running during development
- Use `npx prisma studio` for easy database browsing
- Enable query logging in development:
  ```env
  DATABASE_URL="postgresql://invite_app:dev_password_2024@localhost:5432/supersleek_invite_codes?schema=public&logging=true"
  ```

### For Production

When you're ready to deploy:
- Use a cloud PostgreSQL service (Supabase, Railway, Neon)
- Update your DATABASE_URL environment variable
- Run `npx prisma migrate deploy` instead of `migrate dev`

## Next Steps

After setting up PostgreSQL:

1. **Start the database**: `docker-compose up -d`
2. **Run migrations**: `npx prisma migrate dev --name init`
3. **Seed data**: `npx prisma db seed`
4. **Start the app**: `npm run dev`
5. **Test everything**: `node scripts/test-setup.js`

Your local PostgreSQL database is now ready for development! 🎉
