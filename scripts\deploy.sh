#!/bin/bash

# Supersleek Invite Code - Deployment Script
# This script helps deploy the Shopify app to various platforms

set -e

echo "🚀 Supersleek Invite Code - Deployment Script"
echo "=============================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ Error: .env file not found!"
    echo "Please copy .env.example to .env and configure your environment variables."
    exit 1
fi

# Load environment variables
source .env

# Check required environment variables
required_vars=("DATABASE_URL" "SHOPIFY_API_KEY" "SHOPIFY_API_SECRET")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ Error: Missing required environment variables:"
    printf '%s\n' "${missing_vars[@]}"
    echo "Please update your .env file with the missing variables."
    exit 1
fi

echo "✅ Environment variables check passed"

# Function to deploy to Vercel
deploy_vercel() {
    echo "🔄 Deploying to Vercel..."
    
    # Check if Vercel CLI is installed
    if ! command -v vercel &> /dev/null; then
        echo "Installing Vercel CLI..."
        npm install -g vercel
    fi
    
    # Build the application
    echo "Building application..."
    npm run build
    
    # Deploy to Vercel
    echo "Deploying to Vercel..."
    vercel --prod
    
    echo "✅ Deployed to Vercel successfully!"
}

# Function to deploy to Railway
deploy_railway() {
    echo "🔄 Deploying to Railway..."
    
    # Check if Railway CLI is installed
    if ! command -v railway &> /dev/null; then
        echo "Installing Railway CLI..."
        npm install -g @railway/cli
    fi
    
    # Login to Railway (if not already logged in)
    railway login
    
    # Deploy to Railway
    echo "Deploying to Railway..."
    railway up
    
    echo "✅ Deployed to Railway successfully!"
}

# Function to run database migrations
run_migrations() {
    echo "🔄 Running database migrations..."
    
    # Generate Prisma client
    npx prisma generate
    
    # Run migrations
    npx prisma migrate deploy
    
    # Optionally seed the database
    read -p "Do you want to seed the database with sample data? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        npx prisma db seed
        echo "✅ Database seeded successfully!"
    fi
    
    echo "✅ Database migrations completed!"
}

# Function to update Shopify app configuration
update_shopify_config() {
    echo "🔄 Updating Shopify app configuration..."
    
    # Deploy app configuration to Shopify
    npm run deploy
    
    echo "✅ Shopify app configuration updated!"
}

# Function to run tests
run_tests() {
    echo "🔄 Running tests..."
    
    # Install dependencies if needed
    npm install
    
    # Run linting
    npm run lint
    
    # Build the application to check for TypeScript errors
    npm run build
    
    echo "✅ Tests completed successfully!"
}

# Main deployment menu
echo ""
echo "Select deployment option:"
echo "1) Deploy to Vercel"
echo "2) Deploy to Railway"
echo "3) Run database migrations only"
echo "4) Update Shopify app configuration only"
echo "5) Run tests only"
echo "6) Full deployment (migrations + Shopify config + platform)"
echo "7) Exit"

read -p "Enter your choice (1-7): " choice

case $choice in
    1)
        run_tests
        run_migrations
        deploy_vercel
        update_shopify_config
        ;;
    2)
        run_tests
        run_migrations
        deploy_railway
        update_shopify_config
        ;;
    3)
        run_migrations
        ;;
    4)
        update_shopify_config
        ;;
    5)
        run_tests
        ;;
    6)
        echo "🚀 Starting full deployment..."
        run_tests
        run_migrations
        
        echo ""
        echo "Select platform for deployment:"
        echo "1) Vercel"
        echo "2) Railway"
        read -p "Enter your choice (1-2): " platform_choice
        
        case $platform_choice in
            1)
                deploy_vercel
                ;;
            2)
                deploy_railway
                ;;
            *)
                echo "❌ Invalid choice. Skipping platform deployment."
                ;;
        esac
        
        update_shopify_config
        ;;
    7)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Test your app in the Shopify Partners dashboard"
echo "2. Install the app on a development store"
echo "3. Create some invite codes and test the validation"
echo "4. Check the API documentation at: https://your-app-url.com/api/docs"
echo "5. Test the public validation page at: https://your-app-url.com/validate"
echo ""
echo "🔗 Useful links:"
echo "- App Dashboard: https://your-app-url.com/app"
echo "- API Documentation: https://your-app-url.com/api/docs"
echo "- Public Validation: https://your-app-url.com/validate"
echo "- Database Setup Guide: ./DATABASE_SETUP.md"
echo ""
echo "Happy coding! 🚀"
