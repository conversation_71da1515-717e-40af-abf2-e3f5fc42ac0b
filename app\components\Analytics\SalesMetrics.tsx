import {
  Card,
  Text,
  Box,
  InlineStack,
  BlockStack,
  Badge,
  DataTable,
  ProgressBar,
} from "@shopify/polaris";
import type { SalesAnalytics } from "../../models/sales-tracking.server";

interface SalesMetricsProps {
  salesAnalytics: SalesAnalytics;
}

export function SalesMetrics({ salesAnalytics }: SalesMetricsProps) {
  const {
    totalRevenue,
    totalOrders,
    averageOrderValue,
    topPartners,
    topInviteCodes,
    recentSales,
    salesByPeriod,
  } = salesAnalytics;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (date: Date | string) => {
    const dateObj = date instanceof Date ? date : new Date(date);
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(dateObj);
  };

  // Revenue metrics cards
  const revenueMetrics = [
    {
      label: "Total Revenue",
      value: formatCurrency(totalRevenue),
      subtitle: `From ${totalOrders} orders`,
      color: "bg-surface-success-selected" as const,
    },
    {
      label: "Total Orders",
      value: totalOrders.toLocaleString(),
      subtitle: "Completed purchases",
      color: "bg-surface-info-selected" as const,
    },
    {
      label: "Average Order Value",
      value: formatCurrency(averageOrderValue),
      subtitle: "Per transaction",
      color: "bg-surface-warning-selected" as const,
    },
    {
      label: "Active Partners",
      value: topPartners.length.toString(),
      subtitle: "Generating sales",
      color: "bg-surface-brand-selected" as const,
    },
  ];

  // Top partners table
  const partnerRows = topPartners.slice(0, 5).map((partner) => [
    <Box key={`partner-${partner.partnerId}`}>
      <BlockStack gap="100">
        <Text variant="bodyMd" fontWeight="medium">
          {partner.partnerName}
        </Text>
        {partner.partnerId === null && (
          <Badge tone="info">Unassigned</Badge>
        )}
      </BlockStack>
    </Box>,
    
    <Text key={`orders-${partner.partnerId}`} variant="bodyMd">
      {partner.totalOrders}
    </Text>,
    
    <Text key={`revenue-${partner.partnerId}`} variant="bodyMd" fontWeight="medium">
      {formatCurrency(partner.totalRevenue)}
    </Text>,
    
    <Text key={`aov-${partner.partnerId}`} variant="bodyMd">
      {formatCurrency(partner.averageOrderValue)}
    </Text>,
  ]);

  // Top invite codes table
  const codeRows = topInviteCodes.slice(0, 5).map((code) => [
    <Box key={`code-${code.code}`}>
      <BlockStack gap="100">
        <Text variant="bodyMd" fontWeight="medium">
          {code.code}
        </Text>
        {code.partnerName && (
          <Text variant="bodySm" tone="subdued">
            Partner: {code.partnerName}
          </Text>
        )}
      </BlockStack>
    </Box>,
    
    <Text key={`code-orders-${code.code}`} variant="bodyMd">
      {code.orders}
    </Text>,
    
    <Text key={`code-revenue-${code.code}`} variant="bodyMd" fontWeight="medium">
      {formatCurrency(code.revenue)}
    </Text>,
    
    <Box key={`code-conversion-${code.code}`}>
      <InlineStack gap="200" align="start">
        <Text variant="bodyMd">
          {code.conversionRate.toFixed(1)}%
        </Text>
        <Badge tone={code.conversionRate >= 20 ? "success" : code.conversionRate >= 10 ? "attention" : "critical"}>
          {code.conversionRate >= 20 ? "High" : code.conversionRate >= 10 ? "Good" : "Low"}
        </Badge>
      </InlineStack>
    </Box>,
  ]);

  return (
    <BlockStack gap="400">
      {/* Revenue Overview Cards */}
      <Card>
        <Box padding="400">
          <BlockStack gap="400">
            <Text variant="headingMd" as="h3">
              Sales Performance
            </Text>
            
            <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))", gap: "16px" }}>
              {revenueMetrics.map((metric, index) => (
                <Box
                  key={index}
                  padding="300"
                  background={metric.color}
                  borderRadius="200"
                >
                  <BlockStack gap="200">
                    <Text variant="bodyMd" tone="subdued">
                      {metric.label}
                    </Text>
                    <Text variant="headingLg" as="h4">
                      {metric.value}
                    </Text>
                    <Text variant="bodySm" tone="subdued">
                      {metric.subtitle}
                    </Text>
                  </BlockStack>
                </Box>
              ))}
            </div>
          </BlockStack>
        </Box>
      </Card>

      {/* Sales Trend */}
      <Card>
        <Box padding="400">
          <BlockStack gap="400">
            <Text variant="headingMd" as="h3">
              7-Day Sales Trend
            </Text>
            
            <div style={{ display: "grid", gridTemplateColumns: "repeat(7, 1fr)", gap: "8px" }}>
              {salesByPeriod.map((period, index) => (
                <Box key={index} padding="200" background="bg-surface-secondary" borderRadius="100">
                  <BlockStack gap="100" align="center">
                    <Text variant="bodySm" fontWeight="medium">
                      {period.period}
                    </Text>
                    <Text variant="bodyMd" fontWeight="bold">
                      {formatCurrency(period.revenue)}
                    </Text>
                    <Text variant="bodySm" tone="subdued">
                      {period.orders} orders
                    </Text>
                  </BlockStack>
                </Box>
              ))}
            </div>
          </BlockStack>
        </Box>
      </Card>

      {/* Top Partners */}
      <Card>
        <Box padding="400">
          <BlockStack gap="400">
            <InlineStack align="space-between">
              <Text variant="headingMd" as="h3">
                Top Performing Partners
              </Text>
              <Badge tone="info">
                By Revenue
              </Badge>
            </InlineStack>
            
            <DataTable
              columnContentTypes={["text", "numeric", "numeric", "numeric"]}
              headings={["Partner", "Orders", "Revenue", "Avg Order Value"]}
              rows={partnerRows}
              footerContent={
                topPartners.length > 5 ? (
                  <Text variant="bodySm" tone="subdued">
                    Showing top 5 of {topPartners.length} partners
                  </Text>
                ) : undefined
              }
            />
          </BlockStack>
        </Box>
      </Card>

      {/* Top Invite Codes */}
      <Card>
        <Box padding="400">
          <BlockStack gap="400">
            <InlineStack align="space-between">
              <Text variant="headingMd" as="h3">
                Top Performing Invite Codes
              </Text>
              <Badge tone="info">
                By Revenue
              </Badge>
            </InlineStack>
            
            <DataTable
              columnContentTypes={["text", "numeric", "numeric", "text"]}
              headings={["Invite Code", "Orders", "Revenue", "Conversion"]}
              rows={codeRows}
              footerContent={
                topInviteCodes.length > 5 ? (
                  <Text variant="bodySm" tone="subdued">
                    Showing top 5 of {topInviteCodes.length} invite codes
                  </Text>
                ) : undefined
              }
            />
          </BlockStack>
        </Box>
      </Card>

      {/* Recent Sales */}
      <Card>
        <Box padding="400">
          <BlockStack gap="400">
            <Text variant="headingMd" as="h3">
              Recent Sales
            </Text>
            
            <BlockStack gap="300">
              {recentSales.slice(0, 5).map((sale, index) => (
                <Box
                  key={`sale-${sale.id}-${index}`}
                  padding="300"
                  background="bg-surface-secondary"
                  borderRadius="200"
                >
                  <InlineStack gap="300" align="space-between">
                    <BlockStack gap="100">
                      <InlineStack gap="200" align="start">
                        <Text variant="bodyMd" fontWeight="medium">
                          {sale.inviteCode}
                        </Text>
                        <Badge tone="success">
                          {sale.orderTag}
                        </Badge>
                      </InlineStack>
                      <Text variant="bodySm" tone="subdued">
                        {sale.partnerName || "Unassigned Partner"} • {sale.customerEmail}
                      </Text>
                      <Text variant="bodySm" tone="subdued">
                        {formatDate(sale.orderDate)} • Order #{sale.orderNumber}
                      </Text>
                    </BlockStack>
                    
                    <BlockStack gap="100" align="end">
                      <Text variant="bodyMd" fontWeight="bold">
                        {formatCurrency(sale.orderValue)}
                      </Text>
                      {sale.isSimulated && (
                        <Badge tone="info">Simulated</Badge>
                      )}
                    </BlockStack>
                  </InlineStack>
                </Box>
              ))}
            </BlockStack>
            
            {recentSales.length === 0 && (
              <Box padding="400" textAlign="center">
                <Text variant="bodyMd" tone="subdued">
                  No sales recorded yet. Simulate some orders to see sales data here.
                </Text>
              </Box>
            )}
          </BlockStack>
        </Box>
      </Card>
    </BlockStack>
  );
}
