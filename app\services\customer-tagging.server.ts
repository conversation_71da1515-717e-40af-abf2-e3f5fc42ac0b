/**
 * Shopify Customer Tagging Service
 * Handles adding tags and metafields to customers when they use invite codes
 */

import type { AdminApiContext } from "@shopify/shopify-app-remix/server";
import { getMembershipTagById } from "../models/membership-tag.server";

export interface CustomerTaggingOptions {
  customerId: string;
  inviteCode: string;
  orderId?: string;
  admin: AdminApiContext;
}

export interface MembershipTaggingOptions {
  customerId: string;
  membershipId: string;
  shop: string;
  admin: AdminApiContext;
}

/**
 * Add invite code tag to a Shopify customer
 * Tag format: invite_code:WELCOME25
 */
export async function tagCustomerWithInviteCode({
  customerId,
  inviteCode,
  orderId,
  admin,
}: CustomerTaggingOptions): Promise<{ success: boolean; message: string }> {
  console.log(`🏷️ Tagging customer ${customerId} with invite code: ${inviteCode}`);

  try {
    // First, get the current customer data
    const customerResponse = await admin.graphql(
      `#graphql
        query getCustomer($id: ID!) {
          customer(id: $id) {
            id
            tags
            email
            firstName
            lastName
          }
        }`,
      {
        variables: {
          id: `gid://shopify/Customer/${customerId}`,
        },
      }
    );

    const customerData = await customerResponse.json();
    
    if (!customerData.data?.customer) {
      console.log(`❌ Customer ${customerId} not found`);
      return {
        success: false,
        message: `Customer ${customerId} not found`,
      };
    }

    const customer = customerData.data.customer;
    console.log(`👤 Found customer: ${customer.email} (${customer.firstName} ${customer.lastName})`);

    // Create the invite code tag
    const inviteCodeTag = `invite_code:${inviteCode}`;
    
    // Get existing tags and add the new one if it doesn't exist
    const existingTags = customer.tags || [];
    const hasInviteCodeTag = existingTags.some((tag: string) => 
      tag.startsWith('invite_code:') && tag.includes(inviteCode)
    );

    if (hasInviteCodeTag) {
      console.log(`ℹ️ Customer ${customerId} already has invite code tag: ${inviteCodeTag}`);
      return {
        success: true,
        message: `Customer already tagged with invite code ${inviteCode}`,
      };
    }

    // Add the new tag
    const updatedTags = [...existingTags, inviteCodeTag];

    // Update the customer with the new tag
    const updateResponse = await admin.graphql(
      `#graphql
        mutation customerUpdate($input: CustomerInput!) {
          customerUpdate(input: $input) {
            customer {
              id
              tags
            }
            userErrors {
              field
              message
            }
          }
        }`,
      {
        variables: {
          input: {
            id: customer.id,
            tags: updatedTags,
          },
        },
      }
    );

    const updateData = await updateResponse.json();

    if (updateData.data?.customerUpdate?.userErrors?.length > 0) {
      const errors = updateData.data.customerUpdate.userErrors;
      console.error(`❌ Error updating customer tags:`, errors);
      return {
        success: false,
        message: `Failed to update customer tags: ${errors.map((e: any) => e.message).join(', ')}`,
      };
    }

    console.log(`✅ Successfully tagged customer ${customerId} with invite code: ${inviteCodeTag}`);
    
    // Also add a metafield for more detailed tracking
    await addInviteCodeMetafield({
      customerId,
      inviteCode,
      orderId,
      admin,
    });

    return {
      success: true,
      message: `Customer successfully tagged with invite code ${inviteCode}`,
    };

  } catch (error: any) {
    console.error(`❌ Error tagging customer ${customerId}:`, error);
    return {
      success: false,
      message: `Failed to tag customer: ${error.message}`,
    };
  }
}

/**
 * Apply membership tag to a customer based on invite code's membership
 */
export async function applyMembershipTagToCustomer({
  customerId,
  membershipId,
  shop,
  admin,
}: MembershipTaggingOptions): Promise<{ success: boolean; message: string }> {
  console.log(`🏷️ Applying membership tag to customer ${customerId} for membership: ${membershipId}`);

  try {
    // Get the membership tag configuration
    const membershipTag = await getMembershipTagById(parseInt(membershipId), shop);

    if (!membershipTag) {
      console.log(`⚠️ No membership tag found for membership ID: ${membershipId}`);
      return {
        success: true,
        message: `No membership tag configured for membership ${membershipId}`,
      };
    }

    console.log(`📋 Found membership tag: ${membershipTag.name} -> ${membershipTag.customerTag}`);

    // Get customer details
    const customerResponse = await admin.graphql(
      `#graphql
        query getCustomer($id: ID!) {
          customer(id: $id) {
            id
            tags
            defaultEmailAddress {
              emailAddress
            }
            firstName
            lastName
          }
        }`,
      {
        variables: {
          id: `gid://shopify/Customer/${customerId}`,
        },
      }
    );

    const customerData = await customerResponse.json();

    if (!customerData.data?.customer) {
      console.log(`❌ Customer ${customerId} not found`);
      return {
        success: false,
        message: `Customer ${customerId} not found`,
      };
    }

    const customer = customerData.data.customer;
    console.log(`👤 Found customer: ${customer.defaultEmailAddress?.emailAddress} (${customer.firstName} ${customer.lastName})`);

    // Check if customer already has this membership tag
    const existingTags = customer.tags || [];
    const hasMembershipTag = existingTags.includes(membershipTag.customerTag);

    if (hasMembershipTag) {
      console.log(`ℹ️ Customer ${customerId} already has membership tag: ${membershipTag.customerTag}`);
      return {
        success: true,
        message: `Customer already has membership tag ${membershipTag.customerTag}`,
      };
    }

    // Add the membership tag
    const updatedTags = [...existingTags, membershipTag.customerTag];

    // Update the customer with the new tag
    const updateResponse = await admin.graphql(
      `#graphql
        mutation customerUpdate($input: CustomerInput!) {
          customerUpdate(input: $input) {
            customer {
              id
              tags
            }
            userErrors {
              field
              message
            }
          }
        }`,
      {
        variables: {
          input: {
            id: customer.id,
            tags: updatedTags,
          },
        },
      }
    );

    const updateData = await updateResponse.json();

    if (updateData.data?.customerUpdate?.userErrors?.length > 0) {
      const errors = updateData.data.customerUpdate.userErrors;
      console.error(`❌ Error updating customer with membership tag:`, errors);
      return {
        success: false,
        message: `Failed to apply membership tag: ${errors.map((e: any) => e.message).join(', ')}`,
      };
    }

    console.log(`✅ Successfully applied membership tag ${membershipTag.customerTag} to customer ${customerId}`);

    return {
      success: true,
      message: `Customer successfully tagged with membership ${membershipTag.name}`,
    };

  } catch (error: any) {
    console.error(`❌ Error applying membership tag to customer ${customerId}:`, error);
    return {
      success: false,
      message: `Failed to apply membership tag: ${error.message}`,
    };
  }
}

/**
 * Add invite code metafield to customer for detailed tracking
 */
export async function addInviteCodeMetafield({
  customerId,
  inviteCode,
  orderId,
  admin,
}: CustomerTaggingOptions): Promise<{ success: boolean; message: string }> {
  console.log(`📝 Adding invite code metafield to customer ${customerId}`);

  try {
    const metafieldValue = {
      invite_code: inviteCode,
      used_at: new Date().toISOString(),
      order_id: orderId || null,
    };

    const metafieldResponse = await admin.graphql(
      `#graphql
        mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
          metafieldsSet(metafields: $metafields) {
            metafields {
              id
              key
              value
            }
            userErrors {
              field
              message
            }
          }
        }`,
      {
        variables: {
          metafields: [
            {
              ownerId: `gid://shopify/Customer/${customerId}`,
              namespace: "invite_codes",
              key: "usage_history",
              value: JSON.stringify(metafieldValue),
              type: "json",
            },
          ],
        },
      }
    );

    const metafieldData = await metafieldResponse.json();

    if (metafieldData.data?.metafieldsSet?.userErrors?.length > 0) {
      const errors = metafieldData.data.metafieldsSet.userErrors;
      console.error(`❌ Error adding metafield:`, errors);
      return {
        success: false,
        message: `Failed to add metafield: ${errors.map((e: any) => e.message).join(', ')}`,
      };
    }

    console.log(`✅ Successfully added invite code metafield to customer ${customerId}`);
    return {
      success: true,
      message: `Metafield added successfully`,
    };

  } catch (error: any) {
    console.error(`❌ Error adding metafield to customer ${customerId}:`, error);
    return {
      success: false,
      message: `Failed to add metafield: ${error.message}`,
    };
  }
}

/**
 * Get all invite codes used by a customer
 */
export async function getCustomerInviteCodes(
  customerId: string,
  admin: AdminApiContext
): Promise<string[]> {
  console.log(`🔍 Getting invite codes for customer ${customerId}`);

  try {
    const customerResponse = await admin.graphql(
      `#graphql
        query getCustomer($id: ID!) {
          customer(id: $id) {
            id
            tags
            metafields(namespace: "invite_codes", first: 10) {
              edges {
                node {
                  key
                  value
                }
              }
            }
          }
        }`,
      {
        variables: {
          id: `gid://shopify/Customer/${customerId}`,
        },
      }
    );

    const customerData = await customerResponse.json();
    
    if (!customerData.data?.customer) {
      console.log(`❌ Customer ${customerId} not found`);
      return [];
    }

    const customer = customerData.data.customer;
    
    // Extract invite codes from tags
    const inviteCodesFromTags = customer.tags
      .filter((tag: string) => tag.startsWith('invite_code:'))
      .map((tag: string) => tag.replace('invite_code:', ''));

    console.log(`✅ Found ${inviteCodesFromTags.length} invite codes for customer ${customerId}`);
    return inviteCodesFromTags;

  } catch (error: any) {
    console.error(`❌ Error getting customer invite codes:`, error);
    return [];
  }
}

/**
 * Remove invite code tag from customer (if needed)
 */
export async function removeInviteCodeTag(
  customerId: string,
  inviteCode: string,
  admin: AdminApiContext
): Promise<{ success: boolean; message: string }> {
  console.log(`🗑️ Removing invite code tag from customer ${customerId}: ${inviteCode}`);

  try {
    // Get current customer data
    const customerResponse = await admin.graphql(
      `#graphql
        query getCustomer($id: ID!) {
          customer(id: $id) {
            id
            tags
          }
        }`,
      {
        variables: {
          id: `gid://shopify/Customer/${customerId}`,
        },
      }
    );

    const customerData = await customerResponse.json();
    
    if (!customerData.data?.customer) {
      return {
        success: false,
        message: `Customer ${customerId} not found`,
      };
    }

    const customer = customerData.data.customer;
    const inviteCodeTag = `invite_code:${inviteCode}`;
    
    // Remove the specific invite code tag
    const updatedTags = customer.tags.filter((tag: string) => tag !== inviteCodeTag);

    // Update the customer
    const updateResponse = await admin.graphql(
      `#graphql
        mutation customerUpdate($input: CustomerInput!) {
          customerUpdate(input: $input) {
            customer {
              id
              tags
            }
            userErrors {
              field
              message
            }
          }
        }`,
      {
        variables: {
          input: {
            id: customer.id,
            tags: updatedTags,
          },
        },
      }
    );

    const updateData = await updateResponse.json();

    if (updateData.data?.customerUpdate?.userErrors?.length > 0) {
      const errors = updateData.data.customerUpdate.userErrors;
      return {
        success: false,
        message: `Failed to remove tag: ${errors.map((e: any) => e.message).join(', ')}`,
      };
    }

    console.log(`✅ Successfully removed invite code tag from customer ${customerId}`);
    return {
      success: true,
      message: `Invite code tag removed successfully`,
    };

  } catch (error: any) {
    console.error(`❌ Error removing invite code tag:`, error);
    return {
      success: false,
      message: `Failed to remove tag: ${error.message}`,
    };
  }
}
