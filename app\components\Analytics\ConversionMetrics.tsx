import {
  Card,
  Text,
  Box,
  InlineStack,
  BlockStack,
  Badge,
  ProgressBar,
} from "@shopify/polaris";

interface ConversionMetricsProps {
  totalInviteCodes: number;
  activeInviteCodes: number;
  totalApplications: number;
  totalPurchases: number;
  overallConversionRate: number;
}

export function ConversionMetrics({
  totalInviteCodes,
  activeInviteCodes,
  totalApplications,
  totalPurchases,
  overallConversionRate,
}: ConversionMetricsProps) {
  const metrics = [
    {
      label: "Total Invite Codes",
      value: totalInviteCodes.toLocaleString(),
      subtitle: `${activeInviteCodes} active`,
      color: "bg-surface-brand-selected" as const,
    },
    {
      label: "Total Applications",
      value: totalApplications.toLocaleString(),
      subtitle: "Codes used",
      color: "bg-surface-info-selected" as const,
    },
    {
      label: "Total Purchases",
      value: totalPurchases.toLocaleString(),
      subtitle: "Successful conversions",
      color: "bg-surface-success-selected" as const,
    },
    {
      label: "Conversion Rate",
      value: `${overallConversionRate.toFixed(1)}%`,
      subtitle: "Applications to purchases",
      color: "bg-surface-warning-selected" as const,
    },
  ];

  return (
    <Card>
      <Box padding="400">
        <BlockStack gap="400">
          <Text variant="headingMd" as="h3">
            Conversion Overview
          </Text>
          
          <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))", gap: "16px" }}>
            {metrics.map((metric, index) => (
              <Box
                key={index}
                padding="300"
                background={metric.color}
                borderRadius="200"
              >
                <BlockStack gap="200">
                  <Text variant="bodyMd" tone="subdued">
                    {metric.label}
                  </Text>
                  <Text variant="headingLg" as="h4">
                    {metric.value}
                  </Text>
                  <Text variant="bodySm" tone="subdued">
                    {metric.subtitle}
                  </Text>
                </BlockStack>
              </Box>
            ))}
          </div>

          {/* Conversion Rate Progress Bar */}
          <Box paddingBlockStart="400">
            <BlockStack gap="200">
              <InlineStack align="space-between">
                <Text variant="bodyMd" fontWeight="medium">
                  Overall Conversion Performance
                </Text>
                <Badge tone={overallConversionRate >= 20 ? "success" : overallConversionRate >= 10 ? "attention" : "critical"}>
                  {overallConversionRate >= 20 ? "Excellent" : overallConversionRate >= 10 ? "Good" : "Needs Improvement"}
                </Badge>
              </InlineStack>
              <ProgressBar 
                progress={Math.min(overallConversionRate, 100)} 
                size="small"
                tone={overallConversionRate >= 20 ? "success" : overallConversionRate >= 10 ? "primary" : "critical"}
              />
              <Text variant="bodySm" tone="subdued">
                {overallConversionRate.toFixed(1)}% of applications result in purchases
              </Text>
            </BlockStack>
          </Box>
        </BlockStack>
      </Box>
    </Card>
  );
}
