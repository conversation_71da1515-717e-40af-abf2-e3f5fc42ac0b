import {
  Card,
  Text,
  Box,
  InlineStack,
  BlockStack,
  Select,
  TextField,
  Button,
  DatePicker,
  Popover,
  Icon,
} from "@shopify/polaris";
import { CalendarIcon } from "@shopify/polaris-icons";
import { useState, useCallback } from "react";
import type { PartnerConversionData } from "../../models/analytics.server";

interface AnalyticsFiltersProps {
  partnerPerformance: PartnerConversionData[];
  onFiltersChange: (filters: {
    dateFrom?: Date;
    dateTo?: Date;
    partnerId?: number;
    membershipId?: string;
  }) => void;
  loading?: boolean;
}

export function AnalyticsFilters({ 
  partnerPerformance, 
  onFiltersChange, 
  loading = false 
}: AnalyticsFiltersProps) {
  const [selectedPartner, setSelectedPartner] = useState<string>("");
  const [membershipId, setMembershipId] = useState<string>("");
  const [dateFromPopoverActive, setDateFromPopoverActive] = useState(false);
  const [dateToPopoverActive, setDateToPopoverActive] = useState(false);
  const [dateFrom, setDateFrom] = useState<Date | undefined>();
  const [dateTo, setDateTo] = useState<Date | undefined>();

  // Create partner options for dropdown
  const partnerOptions = [
    { label: "All Partners", value: "" },
    ...partnerPerformance
      .filter(partner => partner.partnerId !== null)
      .map(partner => ({
        label: partner.partnerName,
        value: partner.partnerId!.toString(),
      })),
  ];

  const handlePartnerChange = useCallback((value: string) => {
    setSelectedPartner(value);
    applyFilters({
      partnerId: value ? parseInt(value) : undefined,
      membershipId: membershipId || undefined,
      dateFrom,
      dateTo,
    });
  }, [membershipId, dateFrom, dateTo]);

  const handleMembershipChange = useCallback((value: string) => {
    setMembershipId(value);
    applyFilters({
      partnerId: selectedPartner ? parseInt(selectedPartner) : undefined,
      membershipId: value || undefined,
      dateFrom,
      dateTo,
    });
  }, [selectedPartner, dateFrom, dateTo]);

  const handleDateFromChange = useCallback((value: Date) => {
    setDateFrom(value);
    setDateFromPopoverActive(false);
    applyFilters({
      partnerId: selectedPartner ? parseInt(selectedPartner) : undefined,
      membershipId: membershipId || undefined,
      dateFrom: value,
      dateTo,
    });
  }, [selectedPartner, membershipId, dateTo]);

  const handleDateToChange = useCallback((value: Date) => {
    setDateTo(value);
    setDateToPopoverActive(false);
    applyFilters({
      partnerId: selectedPartner ? parseInt(selectedPartner) : undefined,
      membershipId: membershipId || undefined,
      dateFrom,
      dateTo: value,
    });
  }, [selectedPartner, membershipId, dateFrom]);

  const applyFilters = useCallback((filters: {
    partnerId?: number;
    membershipId?: string;
    dateFrom?: Date;
    dateTo?: Date;
  }) => {
    onFiltersChange(filters);
  }, [onFiltersChange]);

  const clearFilters = useCallback(() => {
    setSelectedPartner("");
    setMembershipId("");
    setDateFrom(undefined);
    setDateTo(undefined);
    onFiltersChange({});
  }, [onFiltersChange]);

  const hasActiveFilters = selectedPartner || membershipId || dateFrom || dateTo;

  const formatDate = (date: Date | undefined) => {
    if (!date) return "";
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  return (
    <Card>
      <Box padding="400">
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text variant="headingMd" as="h3">
              Analytics Filters
            </Text>
            {hasActiveFilters && (
              <Button
                variant="plain"
                onClick={clearFilters}
                disabled={loading}
              >
                Clear Filters
              </Button>
            )}
          </InlineStack>

          <InlineStack gap="400" align="start">
            {/* Partner Filter */}
            <Box style={{ minWidth: "200px" }}>
              <Select
                label="Partner"
                options={partnerOptions}
                value={selectedPartner}
                onChange={handlePartnerChange}
                disabled={loading}
              />
            </Box>

            {/* Membership ID Filter */}
            <Box style={{ minWidth: "200px" }}>
              <TextField
                label="Membership ID"
                value={membershipId}
                onChange={handleMembershipChange}
                placeholder="Filter by membership..."
                disabled={loading}
                autoComplete="off"
              />
            </Box>

            {/* Date From Filter */}
            <Box style={{ minWidth: "150px" }}>
              <Popover
                active={dateFromPopoverActive}
                activator={
                  <TextField
                    label="From Date"
                    value={formatDate(dateFrom)}
                    onFocus={() => setDateFromPopoverActive(true)}
                    suffix={<Icon source={CalendarIcon} />}
                    readOnly
                    disabled={loading}
                  />
                }
                onClose={() => setDateFromPopoverActive(false)}
              >
                <Box padding="400">
                  <DatePicker
                    month={dateFrom?.getMonth() || new Date().getMonth()}
                    year={dateFrom?.getFullYear() || new Date().getFullYear()}
                    selected={dateFrom}
                    onMonthChange={(month, year) => {
                      // Handle month/year navigation if needed
                    }}
                    onChange={handleDateFromChange}
                  />
                </Box>
              </Popover>
            </Box>

            {/* Date To Filter */}
            <Box style={{ minWidth: "150px" }}>
              <Popover
                active={dateToPopoverActive}
                activator={
                  <TextField
                    label="To Date"
                    value={formatDate(dateTo)}
                    onFocus={() => setDateToPopoverActive(true)}
                    suffix={<Icon source={CalendarIcon} />}
                    readOnly
                    disabled={loading}
                  />
                }
                onClose={() => setDateToPopoverActive(false)}
              >
                <Box padding="400">
                  <DatePicker
                    month={dateTo?.getMonth() || new Date().getMonth()}
                    year={dateTo?.getFullYear() || new Date().getFullYear()}
                    selected={dateTo}
                    onMonthChange={(month, year) => {
                      // Handle month/year navigation if needed
                    }}
                    onChange={handleDateToChange}
                  />
                </Box>
              </Popover>
            </Box>
          </InlineStack>

          {hasActiveFilters && (
            <Box padding="200" background="bg-surface-info" borderRadius="200">
              <Text variant="bodySm" tone="subdued">
                {selectedPartner && `Partner: ${partnerOptions.find(p => p.value === selectedPartner)?.label}`}
                {selectedPartner && (membershipId || dateFrom || dateTo) && " • "}
                {membershipId && `Membership: ${membershipId}`}
                {membershipId && (dateFrom || dateTo) && " • "}
                {dateFrom && `From: ${formatDate(dateFrom)}`}
                {dateFrom && dateTo && " • "}
                {dateTo && `To: ${formatDate(dateTo)}`}
              </Text>
            </Box>
          )}
        </BlockStack>
      </Box>
    </Card>
  );
}
