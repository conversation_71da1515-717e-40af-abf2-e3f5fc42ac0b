/* Eye-Friendly Theme Variables */
:root {
  /* Light Mode Colors - Soft and Reduced Blue Light */
  --color-primary: #4A6670;
  --color-primary-light: #6B8A96;
  --color-primary-dark: #3A5259;
  --color-secondary: #E8846B;
  --color-secondary-light: #F2A085;
  --color-secondary-dark: #D66B4A;
  
  /* Background Colors */
  --color-bg-primary: #F8F9FA;
  --color-bg-secondary: #FFFFFF;
  --color-bg-tertiary: #F1F3F4;
  --color-bg-accent: #E8F4F8;
  
  /* Text Colors */
  --color-text-primary: #333740;
  --color-text-secondary: #5F6368;
  --color-text-tertiary: #80868B;
  --color-text-inverse: #FFFFFF;
  
  /* Border Colors */
  --color-border-light: #E8EAED;
  --color-border-medium: #DADCE0;
  --color-border-dark: #BDC1C6;
  
  /* Status Colors - Muted for Eye Comfort */
  --color-success: #4F7942;
  --color-warning: #B8860B;
  --color-error: #C5554D;
  --color-info: --color-primary;
  
  /* Shadows - Soft and Subtle */
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.12);
  --shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.16);
  
  /* Border Radius */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  
  /* Typography */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  
  /* Line Heights for Readability */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
}

/* Dark Mode Colors */
[data-theme="dark"] {
  --color-primary: #6B8A96;
  --color-primary-light: #8BA5B2;
  --color-primary-dark: #4A6670;
  --color-secondary: #F2A085;
  --color-secondary-light: #F5B399;
  --color-secondary-dark: #E8846B;
  
  /* Dark Background Colors */
  --color-bg-primary: #1A1D23;
  --color-bg-secondary: #2D3748;
  --color-bg-tertiary: #374151;
  --color-bg-accent: #1F2937;
  
  /* Dark Text Colors */
  --color-text-primary: #E2E8F0;
  --color-text-secondary: #CBD5E0;
  --color-text-tertiary: #A0AEC0;
  --color-text-inverse: #1A1D23;
  
  /* Dark Border Colors */
  --color-border-light: #374151;
  --color-border-medium: #4B5563;
  --color-border-dark: #6B7280;
  
  /* Dark Status Colors */
  --color-success: #68D391;
  --color-warning: #F6E05E;
  --color-error: #FC8181;
  --color-info: var(--color-primary);
  
  /* Adjusted Shadows for Dark Mode */
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.4);
  --shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.5);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  margin: 0;
  padding: 0;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles for Accessibility */
:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --color-text-primary: #000000;
    --color-bg-primary: #FFFFFF;
    --color-border-medium: #000000;
  }
  
  [data-theme="dark"] {
    --color-text-primary: #FFFFFF;
    --color-bg-primary: #000000;
    --color-border-medium: #FFFFFF;
  }
}

/* Utility Classes */
.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }

.bg-primary { background-color: var(--color-bg-primary); }
.bg-secondary { background-color: var(--color-bg-secondary); }
.bg-accent { background-color: var(--color-bg-accent); }

.border-light { border-color: var(--color-border-light); }
.border-medium { border-color: var(--color-border-medium); }
.border-dark { border-color: var(--color-border-dark); }

.shadow-light { box-shadow: var(--shadow-light); }
.shadow-medium { box-shadow: var(--shadow-medium); }
.shadow-heavy { box-shadow: var(--shadow-heavy); }

.rounded-sm { border-radius: var(--radius-small); }
.rounded-md { border-radius: var(--radius-medium); }
.rounded-lg { border-radius: var(--radius-large); }

/* Spacing Utilities */
.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }

.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }

/* Typography Utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

/* Enhanced Polaris Component Styling for Dark Mode */

/* Enhanced card styling */
.Polaris-Card {
  transition: all 0.2s ease !important;
  border-radius: var(--radius-medium) !important;
  border: 1px solid var(--color-border-light) !important;
  background: var(--color-bg-secondary) !important;
  box-shadow: var(--shadow-light) !important;
}

.Polaris-Card:hover {
  box-shadow: var(--shadow-medium) !important;
  transform: translateY(-1px);
}

/* Enhanced text styling for dark mode */
[data-theme="dark"] .Polaris-Text--root,
[data-theme="dark"] .Polaris-DisplayText--root,
[data-theme="dark"] .Polaris-Heading--root {
  color: var(--color-text-primary) !important;
}

[data-theme="dark"] .Polaris-Text--subdued {
  color: var(--color-text-secondary) !important;
}

/* Enhanced button styling */
.Polaris-Button {
  transition: all 0.2s ease !important;
  border-radius: var(--radius-medium) !important;
}

.Polaris-Button--primary {
  background: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

.Polaris-Button--primary:hover {
  background: var(--color-primary-dark) !important;
  border-color: var(--color-primary-dark) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium) !important;
}

/* Enhanced form field styling */
.Polaris-TextField__Input,
.Polaris-Select__Input {
  background: var(--color-bg-secondary) !important;
  border-color: var(--color-border-medium) !important;
  color: var(--color-text-primary) !important;
  transition: all 0.2s ease !important;
}

.Polaris-TextField__Input:focus,
.Polaris-Select__Input:focus {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 2px rgba(107, 138, 150, 0.2) !important;
}

/* Enhanced badge styling */
.Polaris-Badge {
  border-radius: calc(var(--radius-medium) / 2) !important;
}

.Polaris-Badge--statusSuccess {
  background: var(--color-success) !important;
  color: var(--color-text-inverse) !important;
}

.Polaris-Badge--statusCritical {
  background: var(--color-error) !important;
  color: var(--color-text-inverse) !important;
}

.Polaris-Badge--statusWarning {
  background: var(--color-warning) !important;
  color: var(--color-text-inverse) !important;
}

/* Enhanced data table styling */
.Polaris-DataTable__Table {
  background: var(--color-bg-secondary) !important;
}

.Polaris-DataTable__Cell {
  border-color: var(--color-border-light) !important;
  color: var(--color-text-primary) !important;
}

.Polaris-DataTable__Cell--header {
  background: var(--color-bg-tertiary) !important;
  color: var(--color-text-primary) !important;
  font-weight: 600 !important;
}

/* Enhanced navigation styling */
.Polaris-Navigation {
  background: var(--color-bg-secondary) !important;
  border-color: var(--color-border-light) !important;
}

.Polaris-Navigation__Item {
  color: var(--color-text-primary) !important;
}

.Polaris-Navigation__Item:hover {
  background: var(--color-bg-tertiary) !important;
}

/* Enhanced page header styling */
.Polaris-Page-Header {
  background: var(--color-bg-primary) !important;
  border-color: var(--color-border-light) !important;
}

/* Custom dashboard card enhancements */
.dashboard-card {
  background: var(--color-bg-secondary) !important;
  border: 1px solid var(--color-border-light) !important;
  border-radius: var(--radius-medium) !important;
  transition: all 0.2s ease !important;
  box-shadow: var(--shadow-light) !important;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium) !important;
}

.dashboard-metric {
  color: var(--color-text-primary) !important;
}

.dashboard-metric-value {
  color: var(--color-primary) !important;
  font-weight: 700 !important;
}

.dashboard-metric-label {
  color: var(--color-text-secondary) !important;
}

/* Enhanced loading states */
.Polaris-Spinner {
  color: var(--color-primary) !important;
}

/* Enhanced modal styling */
.Polaris-Modal-Dialog {
  background: var(--color-bg-secondary) !important;
  border-radius: var(--radius-medium) !important;
  box-shadow: var(--shadow-heavy) !important;
}

/* Enhanced toast styling */
.Polaris-Frame-Toast {
  background: var(--color-bg-secondary) !important;
  border: 1px solid var(--color-border-light) !important;
  border-radius: var(--radius-medium) !important;
  color: var(--color-text-primary) !important;
  box-shadow: var(--shadow-medium) !important;
}

/* Enhanced theme toggle button */
.theme-toggle-button {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 1000 !important;
  background: var(--color-bg-secondary) !important;
  border: 1px solid var(--color-border-medium) !important;
  border-radius: 50% !important;
  width: 48px !important;
  height: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: var(--shadow-medium) !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

.theme-toggle-button:hover {
  transform: scale(1.05) !important;
  box-shadow: var(--shadow-heavy) !important;
}

/* Enhanced status indicators */
.status-active {
  color: var(--color-success) !important;
}

.status-inactive {
  color: var(--color-text-tertiary) !important;
}

.status-warning {
  color: var(--color-warning) !important;
}

.status-error {
  color: var(--color-error) !important;
}

/* Stats Grid and Cards Styling */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.stats-card {
  position: relative;
}

.stats-card-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-medium);
  background-color: var(--color-bg-accent);
  color: var(--color-primary);
  opacity: 0.8;
}

.stats-card-success .stats-card-icon {
  background-color: rgba(79, 121, 66, 0.1);
  color: var(--color-success);
}

.stats-card-warning .stats-card-icon {
  background-color: rgba(184, 134, 11, 0.1);
  color: var(--color-warning);
}

.stats-card-critical .stats-card-icon {
  background-color: rgba(197, 85, 77, 0.1);
  color: var(--color-error);
}

.trend-indicator {
  padding: 2px 6px;
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.trend-positive {
  background-color: rgba(79, 121, 66, 0.1);
}

.trend-negative {
  background-color: rgba(197, 85, 77, 0.1);
}

/* Activity Feed Styling */
.activity-feed {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  position: relative;
  padding: var(--space-md) 0;
}

.activity-item:first-child {
  padding-top: 0;
}

.activity-item:last-child {
  padding-bottom: 0;
}

.activity-avatar {
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--color-border-light);
}

.activity-empty-state {
  text-align: center;
  padding: var(--space-xl) 0;
}

/* Quick Stats Styling */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
  text-align: center;
}

.quick-stat-item {
  padding: var(--space-md);
  border-radius: var(--radius-medium);
  background-color: var(--color-bg-accent);
  transition: all 0.2s ease;
}

.quick-stat-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .quick-stats {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .activity-content .Polaris-InlineStack {
    flex-wrap: wrap;
  }
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.4s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Enhanced hover effects */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Enhanced focus states for better accessibility */
.Polaris-Button:focus,
.Polaris-TextField__Input:focus,
.Polaris-Select__Input:focus {
  outline: 2px solid var(--color-primary) !important;
  outline-offset: 2px !important;
}

/* Enhanced scrollbar for dark mode */
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--color-bg-tertiary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--color-border-dark);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-tertiary);
}
