import { Card as PolarisCard, type CardProps } from "@shopify/polaris";
import { forwardRef, type ReactNode } from "react";

interface CustomCardProps extends Omit<CardProps, "children"> {
  children: ReactNode;
  variant?: "default" | "elevated" | "subdued" | "accent";
  padding?: "none" | "sm" | "md" | "lg";
  className?: string;
  hover?: boolean;
  loading?: boolean;
}

export const Card = forwardRef<HTMLDivElement, CustomCardProps>(
  ({ 
    variant = "default", 
    padding = "md", 
    className = "", 
    hover = false,
    loading = false,
    children,
    ...props 
  }, ref) => {
    
    const getCustomClasses = () => {
      const baseClasses = "transition-all";
      const variantClasses = {
        default: "bg-secondary shadow-light",
        elevated: "bg-secondary shadow-medium hover-lift",
        subdued: "bg-tertiary shadow-light",
        accent: "bg-accent shadow-light border-l-4 border-primary"
      };
      
      const paddingClasses = {
        none: "",
        sm: "p-sm",
        md: "p-md", 
        lg: "p-lg"
      };
      
      const hoverClass = hover ? "hover-lift" : "";
      const loadingClass = loading ? "loading" : "";
      
      return `${baseClasses} ${variantClasses[variant]} ${paddingClasses[padding]} ${hoverClass} ${loadingClass}`.trim();
    };

    const combinedClassName = `${getCustomClasses()} ${className}`.trim();

    return (
      <PolarisCard
        ref={ref}
        className={combinedClassName}
        {...props}
      >
        {children}
      </PolarisCard>
    );
  }
);

Card.displayName = "Card";

// Card Section Component
interface CardSectionProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  actions?: ReactNode;
  className?: string;
}

export function CardSection({ 
  children, 
  title, 
  subtitle, 
  actions, 
  className = "" 
}: CardSectionProps) {
  return (
    <div className={`card-section ${className}`}>
      {(title || subtitle || actions) && (
        <div className="card-section-header">
          <div className="card-section-title-area">
            {title && (
              <h3 className="card-section-title text-lg font-semibold text-primary">
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="card-section-subtitle text-sm text-secondary">
                {subtitle}
              </p>
            )}
          </div>
          {actions && (
            <div className="card-section-actions">
              {actions}
            </div>
          )}
        </div>
      )}
      <div className="card-section-content">
        {children}
      </div>
    </div>
  );
}

// Card styles (to be added to CSS)
export const cardStyles = `
  .card-section {
    padding: var(--space-md);
  }
  
  .card-section:not(:last-child) {
    border-bottom: 1px solid var(--color-border-light);
  }
  
  .card-section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-md);
  }
  
  .card-section-title {
    margin: 0 0 var(--space-xs) 0;
  }
  
  .card-section-subtitle {
    margin: 0;
  }
  
  .card-section-actions {
    display: flex;
    gap: var(--space-sm);
    align-items: center;
  }
  
  .card-section-content {
    /* Content styles */
  }
  
  @media (max-width: 768px) {
    .card-section-header {
      flex-direction: column;
      gap: var(--space-sm);
      align-items: stretch;
    }
    
    .card-section-actions {
      justify-content: flex-end;
    }
  }
`;
