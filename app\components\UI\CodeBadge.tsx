import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lt<PERSON> } from "@shopify/polaris";
import { ClipboardIcon } from "@shopify/polaris-icons";
import { useState } from "react";

interface CodeBadgeProps {
  code: string;
  status?: "active" | "inactive" | "expired" | "used";
  size?: "small" | "medium" | "large";
  copyable?: boolean;
  className?: string;
}

export function CodeBadge({ 
  code, 
  status = "active", 
  size = "medium", 
  copyable = true,
  className = "" 
}: CodeBadgeProps) {
  const [copied, setCopied] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "success";
      case "inactive":
        return "attention";
      case "expired":
        return "warning";
      case "used":
        return "info";
      default:
        return "new";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Active";
      case "inactive":
        return "Inactive";
      case "expired":
        return "Expired";
      case "used":
        return "Used";
      default:
        return "Unknown";
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case "small":
        return "text-sm p-xs";
      case "large":
        return "text-lg p-md";
      default:
        return "text-base p-sm";
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy code:", err);
    }
  };

  return (
    <div className={`code-badge ${getSizeClasses(size)} ${className}`}>
      <div className="code-badge-content">
        <span className="code-badge-text" aria-label={`Invite code: ${code}`}>
          {code}
        </span>
        
        <Badge tone={getStatusColor(status) as any} size="small">
          {getStatusText(status)}
        </Badge>
        
        {copyable && (
          <Tooltip content={copied ? "Copied!" : "Copy code"}>
            <Button
              variant="tertiary"
              size="micro"
              icon={ClipboardIcon}
              onClick={copyToClipboard}
              accessibilityLabel={`Copy invite code ${code}`}
              className="code-badge-copy-btn"
            />
          </Tooltip>
        )}
      </div>
    </div>
  );
}

// Status Badge
interface StatusBadgeProps {
  isActive: boolean;
  className?: string;
}

export function StatusBadge({ isActive, className = "" }: StatusBadgeProps) {
  return (
    <Badge
      tone={isActive ? "success" : "critical"}
      size="small"
      className={className}
    >
      {isActive ? "Active" : "Inactive"}
    </Badge>
  );
}

// Usage Statistics Badge
interface UsageBadgeProps {
  usedCount: number;
  maxUses: number;
  className?: string;
}

export function UsageBadge({ usedCount, maxUses, className = "" }: UsageBadgeProps) {
  const percentage = maxUses > 0 ? (usedCount / maxUses) * 100 : 0;
  const isFullyUsed = usedCount >= maxUses;
  const isNearLimit = percentage >= 80;

  const getColor = () => {
    if (isFullyUsed) return "critical";
    if (isNearLimit) return "warning";
    return "success";
  };

  return (
    <div className={`usage-badge ${className}`}>
      <Badge tone={getColor() as any} size="small">
        {usedCount}/{maxUses} uses
      </Badge>
      <div className="usage-progress">
        <div
          className="usage-progress-fill"
          style={{ width: `${Math.min(percentage, 100)}%` }}
          aria-label={`${usedCount} of ${maxUses} uses completed`}
        />
      </div>
    </div>
  );
}

// Expiry Badge
interface ExpiryBadgeProps {
  expiresAt?: Date | null;
  className?: string;
}

export function ExpiryBadge({ expiresAt, className = "" }: ExpiryBadgeProps) {
  if (!expiresAt) {
    return (
      <Badge tone="info" size="small" className={className}>
        No expiry
      </Badge>
    );
  }

  // Convert string to Date if needed
  const expiryDate = typeof expiresAt === 'string' ? new Date(expiresAt) : expiresAt;
  const now = new Date();
  const isExpired = expiryDate < now;
  const timeUntilExpiry = expiryDate.getTime() - now.getTime();
  const daysUntilExpiry = Math.ceil(timeUntilExpiry / (1000 * 60 * 60 * 24));
  const isExpiringSoon = daysUntilExpiry <= 7 && daysUntilExpiry > 0;

  const getColor = () => {
    if (isExpired) return "critical";
    if (isExpiringSoon) return "warning";
    return "success";
  };

  const getText = () => {
    if (isExpired) return "Expired";
    if (daysUntilExpiry === 0) return "Expires today";
    if (daysUntilExpiry === 1) return "Expires tomorrow";
    if (daysUntilExpiry <= 7) return `Expires in ${daysUntilExpiry} days`;
    return `Expires ${expiryDate.toLocaleDateString()}`;
  };

  return (
    <Badge tone={getColor() as any} size="small" className={className}>
      {getText()}
    </Badge>
  );
}

// Code Badge Styles (to be added to CSS)
export const codeBadgeStyles = `
  .code-badge {
    display: inline-flex;
    align-items: center;
    background-color: var(--color-bg-secondary);
    border: 1px solid var(--color-border-medium);
    border-radius: var(--radius-medium);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 600;
    letter-spacing: 0.05em;
  }
  
  .code-badge-content {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
  }
  
  .code-badge-text {
    color: var(--color-text-primary);
    user-select: all;
  }
  
  .code-badge-copy-btn {
    opacity: 0.7;
    transition: opacity 0.2s ease;
  }
  
  .code-badge:hover .code-badge-copy-btn {
    opacity: 1;
  }
  
  .usage-badge {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
    align-items: flex-start;
  }
  
  .usage-progress {
    width: 60px;
    height: 4px;
    background-color: var(--color-bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
  }
  
  .usage-progress-fill {
    height: 100%;
    background-color: var(--color-success);
    transition: width 0.3s ease;
    border-radius: 2px;
  }
  
  .usage-badge .Polaris-Badge[data-tone="warning"] + .usage-progress .usage-progress-fill {
    background-color: var(--color-warning);
  }
  
  .usage-badge .Polaris-Badge[data-tone="critical"] + .usage-progress .usage-progress-fill {
    background-color: var(--color-error);
  }
`;
