-- CreateTable
CREATE TABLE "app_settings" (
    "id" SERIAL NOT NULL,
    "shop" VARCHAR(255) NOT NULL,
    "date_format" VARCHAR(50) NOT NULL DEFAULT 'YYYY-MM-DD',
    "time_format" VARCHAR(10) NOT NULL DEFAULT '24h',
    "timezone" VARCHAR(100) NOT NULL DEFAULT 'UTC',
    "default_expiry_days" INTEGER,
    "enable_email_notifications" BOOLEAN NOT NULL DEFAULT true,
    "max_codes_per_batch" INTEGER NOT NULL DEFAULT 100,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL,

    CONSTRAINT "app_settings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "app_settings_shop_key" ON "app_settings"("shop");

-- CreateIndex
CREATE INDEX "app_settings_shop_idx" ON "app_settings"("shop");
