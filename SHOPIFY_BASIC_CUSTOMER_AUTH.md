# Shopify Basic Plan Customer Authentication

Since Multipass is only available for Shopify Plus plans, we've implemented an alternative solution for Shopify Basic plans that provides a similar seamless customer experience.

## 🔄 **How It Works**

### 1. **Invite Code Validation**
- User enters invite code on validation page
- System validates code (expiry, usage limits, etc.)
- If valid, proceeds to customer account creation

### 2. **Automatic Customer Account Creation**
- Uses Shopify Admin API to create customer account
- Generates secure random password automatically
- Adds invite code tracking via tags and metafields
- Records usage analytics

### 3. **Seamless Login Experience**
- Redirects user to Shopify's standard login page
- Customer can log in with their email and generated password
- Or use "Forgot Password" to set their own password
- Maintains invite code context throughout the process

## 🛠️ **Technical Implementation**

### **Customer Account API Approach**
```typescript
// 1. Create customer via Admin API
const customer = await createCustomer({
  email: "<EMAIL>",
  first_name: "<PERSON>",
  last_name: "<PERSON><PERSON>",
  password: "auto-generated-secure-password",
  tags: ["invite-code:WEL<PERSON><PERSON><PERSON>", "supersleek-user"],
  metafields: [
    {
      namespace: "supersleek",
      key: "invite_code", 
      value: "WELCOME10"
    }
  ]
});

// 2. Generate login URL
const loginUrl = `https://shop.myshopify.com/account/login?return_url=/`;

// 3. Redirect user to login
window.location.href = loginUrl;
```

### **API Endpoints**

#### `POST /api/create-customer`
Creates customer account with invite code tracking.

**Request:**
```json
{
  "inviteCode": "WELCOME10",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "acceptsMarketing": false,
  "returnTo": "/"
}
```

**Response:**
```json
{
  "success": true,
  "customer": {
    "id": 123456,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  },
  "loginUrl": "https://shop.myshopify.com/account/login",
  "loginToken": "temp-token-for-verification",
  "message": "Customer account created successfully"
}
```

#### `GET/POST /api/verify-login`
Verifies temporary login tokens for additional security.

## 🔧 **Required Configuration**

### **Environment Variables**
```env
# Shopify API Access
SHOPIFY_ACCESS_TOKEN="your_admin_api_token"
SHOPIFY_STOREFRONT_ACCESS_TOKEN="your_storefront_token"

# Enhanced Scopes for Customer Management
SHOPIFY_SCOPES="read_customers,write_customers,read_customer_events,write_customer_events,read_orders,write_orders,read_products,write_products,unauthenticated_read_customers,unauthenticated_write_customers"

# Shop Configuration
DEV_SHOP_DOMAIN="supersleek-staging.myshopify.com"
```

### **Required Scopes Explanation**

| Scope | Purpose |
|-------|---------|
| `read_customers` | Read customer information for validation |
| `write_customers` | Create and update customer accounts |
| `read_customer_events` | Track customer activity |
| `write_customer_events` | Log invite code usage events |
| `unauthenticated_read_customers` | Allow public customer lookups |
| `unauthenticated_write_customers` | Allow public customer creation |

## 🎯 **User Experience Flow**

### **Step 1: Validation Page**
```
User visits: /validate?code=WELCOME10&shop=supersleek-staging.myshopify.com
├── Enters invite code
├── Optionally provides email/name
└── Clicks "Validate Code"
```

### **Step 2: Account Creation**
```
System automatically:
├── Validates invite code
├── Creates customer account
├── Records usage analytics
└── Generates login URL
```

### **Step 3: Login & Access**
```
User is redirected to:
├── Shopify login page
├── Can log in with email + generated password
├── Or use "Forgot Password" to set own password
└── Redirected to shop homepage after login
```

## 🔐 **Security Features**

### **Secure Password Generation**
- 16-byte random hex + complexity characters
- Meets Shopify's password requirements
- User can change via "Forgot Password"

### **Temporary Login Tokens**
- Short-lived verification tokens (10 minutes)
- Cryptographically secure
- Single-use only

### **Invite Code Tracking**
- Complete audit trail
- IP address and user agent logging
- Usage analytics and reporting

## 🚀 **Advantages Over Multipass**

### **✅ Available on All Shopify Plans**
- Works with Shopify Basic, Shopify, and Advanced
- No Plus subscription required
- Same seamless experience

### **✅ Enhanced Tracking**
- More detailed analytics than Multipass
- Custom metafields for rich data
- Better integration with your app

### **✅ Flexible Authentication**
- Users can set their own passwords
- Standard Shopify login experience
- Compatible with all Shopify features

### **✅ Better Error Handling**
- Detailed error messages
- Graceful fallbacks
- Better user feedback

## 🔄 **Migration from Multipass**

If you were previously using Multipass:

1. **Update Environment Variables**
   ```env
   # Remove
   SHOPIFY_MULTIPASS_SECRET=""
   
   # Add
   SHOPIFY_ACCESS_TOKEN="your_token"
   SHOPIFY_STOREFRONT_ACCESS_TOKEN="your_storefront_token"
   ```

2. **Update Scopes**
   - Add `unauthenticated_read_customers`
   - Add `unauthenticated_write_customers`

3. **Update Code References**
   ```typescript
   // Old Multipass approach
   import { generateCustomerLoginUrl } from "~/utils/multipass.server";
   
   // New Customer Auth approach
   import { createCustomerWithInviteCode } from "~/utils/customer-auth.server";
   ```

## 🧪 **Testing Your Setup**

### **1. Test Customer Creation**
```bash
curl -X POST http://localhost:3000/api/create-customer \
  -H "Content-Type: application/json" \
  -d '{
    "inviteCode": "WELCOME10",
    "email": "<EMAIL>",
    "firstName": "Test",
    "lastName": "User"
  }'
```

### **2. Test Validation Flow**
Visit: `http://localhost:3000/validate?code=WELCOME10&shop=supersleek-staging.myshopify.com`

### **3. Verify Customer in Shopify Admin**
1. Go to your Shopify Admin
2. Navigate to Customers
3. Look for the created customer
4. Check tags and metafields for invite code tracking

## 📊 **Analytics & Reporting**

The new system provides enhanced analytics:

- **Customer Creation Metrics**: Track successful account creations
- **Invite Code Performance**: Detailed usage statistics
- **Conversion Tracking**: From invite to customer to order
- **Error Analysis**: Failed attempts and reasons

## 🔧 **Troubleshooting**

### **Common Issues**

**Customer Creation Fails**
- Check `SHOPIFY_ACCESS_TOKEN` is valid
- Verify scopes include `write_customers`
- Ensure email is unique in Shopify

**Login Redirect Not Working**
- Verify shop domain is correct
- Check Customer Account settings in Shopify Admin
- Ensure return URL is properly encoded

**Missing Analytics Data**
- Verify `write_customer_events` scope
- Check database connection
- Ensure usage recording is working

## 🎉 **Benefits for Your Users**

1. **No Complex Setup**: Works out of the box on any Shopify plan
2. **Familiar Experience**: Standard Shopify login process
3. **Password Control**: Users can set their own passwords
4. **Better Security**: Modern authentication practices
5. **Rich Tracking**: Detailed analytics and insights

This approach provides all the benefits of Multipass while being available on Shopify Basic plans and offering enhanced functionality!
